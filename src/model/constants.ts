import { AVAILABLE_SEAFARERS } from '../constants/crewPlanner';

export const approvalStatuses = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  FORWARDED: 'forwarded',
  REAPPLIED: 'reapplied',
};

export const screeningApprovalGroups = {
  COMPLIANCE_EMPLOYEE: 'Compliance Employee',
  COMPLIANCE_SUPERVISOR: 'Compliance Supervisor',
  FPD_MANNING: 'Fleet Personnel',
};

export const screeningStatus = {
  ALL: 'all',
  NOT_STARTED: 'not_started',
  DRAFT: 'draft',
  UNDER_SCREENING: 'under_screening',
  PASSED: 'passed',
  REJECTED: 'rejected',
  ARCHIVED: 'archived',
};

export const screeningStatusLabel = {
  under_screening: 'In Screening',
  passed: 'Approved Seafarers',
  rejected: 'Screening Rejected',
  archived: 'Archived Seafarers',
  all: 'All Seafarers',
};

export const screeningStatusColumnLabel = {
  under_screening: 'In Screening',
  passed: 'Approved',
  rejected: 'Rejected',
  archived: 'Archived',
  draft: 'Draft',
  not_started: 'Not Started',
};

export const searchType = {
  PASSPORT: 'passport',
  SEAMANS_BOOK: 'seamans_book',
  PERSONAL_DETAILS: 'personal_details',
};

export const dataQuality = {
  MANDATORY_DATA_MISSING: 'mandatory_data_missing',
  DATA_INVALID: 'data_invalid',
  CLEAN: 'clean',
};

export const LOCAL_STORAGE_FIELDS = {
  masterKey: 'seafarer-table-details',
  masterKeyAvailableTable: `${AVAILABLE_SEAFARERS}-table-details`,
  masterKeyContractExpiry: 'contract-expiry-table-details',
  masterKeyCrewList: 'crew-list-table-details',
  tablePageSize: 'seafarer-table-page-size',
  tableSelectedColumns: 'seafarer-table-selected-columns',
  availableTableSelectedColumns: 'available-seafarers-table-selected-columns',
  contractTableSelectedColumns: 'contract-expiry-table-selected-columns',
  signedOnTableSelectedColumns: 'signed-on-table-selected-columns',
  recommendedTableSelectedColumns: 'recommended-table-selected-columns',
  crewListTableSelectedColumns: 'crew-list-table-selected-columns',
  tablePageIndex: 'seafarer-table-page-index',
  tablePageSort: 'seafarer-table-page-sort',
  advancedSearchParams: 'seafarer-search-criteria',
};

export const shipPartyType = {
  MANNING_AGENT: 'Manning Agency',
};

export const seafarerStatus = {
  INITIALIZED: 'initialized',
  NEW_APPLICANT: 'new_applicant',
  ON_LEAVE: 'on_leave',
  NO_EXAMINATION: 'no_examination',
  RECOMMENDED: 'recommended',
  RECOMMENDED_WITH_DEVIATION: 'recommended_with_deviation',
  CREW_ASSIGNMENT_APPROVED: 'crew_assignment_approved',
  TRAVELLING: 'travelling',
  SIGNED_ON: 'signed_on',
  CREW_ASSIGNMENT_REJECTED: 'crew_assignment_rejected',
  CREW_ASSIGNMENT_ARCHIVED: 'crew_assignment_archived',
  CREW_ASSIGNMENT_PENDING: 'crew_assignment_pending',
  CANCEL_TRAVEL: 'cancelled_travel_plan'
};

export const SEAFARER_STATUS_FIELDS = [
  'account_status',
  'journey_status',
  'exam_status',
  'plan_status',
];

export const EXPERIENCE_IN_YEARS_FIELDS = [
  'duration_with_company',
  'duration_in_target_rank',
  'duration_on_target_vessel_type',
  'duration_on_all_vessel_type',
];

export const POST_JOINING_STATUTES = ['signed_on', 'travelling'];

export const PRE_JOINING_STATUTES = [
  'recommended',
  'recommended_with_deviation',
  'crew_assignment_approved',
];

export const ALLOWED_ALLOTMENT_STATUSES = [
  seafarerStatus.SIGNED_ON,
  seafarerStatus.TRAVELLING,
  seafarerStatus.CREW_ASSIGNMENT_APPROVED,
];

export const ALLOWED_APPOINTMENT_LETTER_STATUSES = [
  seafarerStatus.SIGNED_ON,
  seafarerStatus.TRAVELLING,
];
export const screeningFilterKeys = Object.freeze({
  SCREENING_FOR: 'screeningFor',
  PENDING_APPROVAL_GROUP: 'pendingApprovalGroup',
  SCREENING_SUBMITTED_DATE_AND_TIME: 'screeningSubmittedDateAndTime',
});

export const screeningForColumnMap = Object.freeze({
  NEW_APPLICANT: 'new_applicant',
  VESSEL_RECOMMENDATION: 'vessel_recommendation',
  DATA_EDIT: 'data_edit',
});

export const SCREENING_STATUS = [screeningStatus.UNDER_SCREENING, screeningStatus.REJECTED];

export const approvalColumns = Object.freeze({
  APPROVAL_GROUP: 'Approval group',
  PENDING_APPROVAL_GROUP: 'Pending approval group',
});

export const APPROVAL_GROUPS = [
  approvalColumns.PENDING_APPROVAL_GROUP,
  approvalColumns.APPROVAL_GROUP,
];
export const SCREENING_COLUMNS = [
  ...APPROVAL_GROUPS,
  'Screening for',
  'Screening submitted date & time',
];

export const STATUS_COLORS = {
  ORANGE: 'orange',
  GREEN: 'green',
  RED: 'red',
  BLUE: 'blue'
}
