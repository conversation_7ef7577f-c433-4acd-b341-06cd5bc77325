/* eslint-disable react/display-name */
import React, { useState, useEffect, useRef, forwardRef, useMemo, useCallback } from 'react';
import {
  Container,
  Col,
  Row,
  Carousel,
  Modal,
  Form,
  Button,
  Tab,
  DropdownButton,
  Dropdown,
} from 'react-bootstrap';
import { useParams, useHistory, Switch, Route, useLocation } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import moment from 'moment';
import { seafarerStatusService } from 'paris2-seafarer-status';
import { useAccess } from '@src/component/common/Access';
import { toast } from 'react-toastify';
import NotificationProvider from '@src/component/common/Notification';
import seafarerService from '../service/seafarer-service';
import apptLetterService from '../service/appointment-letter-service';
import vesselService from '../service/vessel-service';
import seafarerSurveyService from '../service/seafarer-survery-service';
import SeafarerGeneralDetails from '../model/SeafarerGeneralDetails';
import SeafarerPersonalParticulars from '../model/SeafarerPersonalParticulars';
import SeafarerPassport from '../model/SeafarerPassport';
import SeafarerSeamanBook from '../model/SeafarerSeamanBook';
import SeafarerNextOfKin from '../model/SeafarerNextOfKin';
import Spinner from '../component/common/Spinner';
import ErrorAlert from '../component/common/ErrorAlert';
import styleGuide from '../styleGuide';
import ScrollArrow from '../component/BackToTopButton';
import ButtonsToolBar from '../component/Details/ButtonsToolBar';
import ContactDetailsSection from '../component/Details/ContactDetailsSection';
import TableHeaderRow from '../component/Details/TableHeaderRow';
import TableMultipleSection from '../component/Details/TableMultipleSection';
import _, { toString, cloneDeep } from 'lodash';
import ImageController from '../controller/image-upload-controller';
import {
  screeningStatus,
  seafarerStatus,
  POST_JOINING_STATUTES,
  ALLOWED_APPOINTMENT_LETTER_STATUSES,
} from '../model/constants';
import {
  stringAsDate,
  dateAsString,
  dateAsDash,
  capitalizeArgs,
  Dash,
  getAddress,
} from '../model/utils';
import './scss/details-page.scss';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import seafarerSchema, { getSeamanBooksSchema } from '../model/SeafarerSchemaValidation';
import ErrorsListComponent from '../component/ErrorsListComponent';
import DuplicateSeafarerAlert from '../component/Details/DuplicateSeafarerAlert';
import MarkAsDuplicateModal from '../component/Details/MarkAsDuplicateModal';
import DuplicateChildHKIDInfo from '../component/Details/DuplicateChildHKID';
import SeafarerExperienceSummary from '../component/seafarerExperience/SeafarerExperienceSummary';
import ScreeningPage from './ScreeningPage';
import StatusHistoryPage from './StatusHistoryPage';
import DocumentsPage from './DocumentsPage';
import AddDocumentModal from '../component/document/AddDocumentModal';
import EndorsementAndVerification from './EndorsementAndVerification';
import AvailabilityPage from './AvailabilityPage';

import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import {
  checkUserBelongToSameShipParty,
  filterByContactType,
  getStatusHistoryWithLatestVesselOwnershipData,
  twoDigitFloat,
} from '../util/view-utils';
import AddSeafarerController from '../controller/add-seafarer-controller';
import { InviteUserAccountModal } from '../component/Details/InviteUserAccountModal';
import { EMAIL } from '../constants/contactTypes';
import { ConfirmationModal } from '../component/common/ConfirmationModal';
import { createKeycloakAccount } from '../service/user-service';
import AccountDetailsPage from './AccountDetailsPage';
import TabWrapper from '../component/common/TabWrapper';
import { DetailPageTabData } from '../model/TabData';
import OtherDocumentsPage from './OtherDocumentsPage';
import PreJoiningTab from '../component/PreJoining/PreJoiningTab';
import UpdateWagesModal from '../component/UpdateWages/UpdateWagesModal';
import WagesHistoryModal from '../component/seafarerExperience/WagesHistoryModal';
import ViewCrewAssignmentPlan from '../component/CrewAssignment/ViewCrewAssignmentPlan';
import TravelModal from '../component/travel-modal/travel-modal';
import UnableToRecommendModal from '../component/Details/unable-to-recommend-modal/unable-to-recommend-modal';
import AppraisalsPage from './AppraisalsPage';
import SuptAppraisalSection from '../component/appraisals/SuptAppraisalSection';
import {
  WAGES_STATUS_PENDING,
  WAGES_STATUS_APPLIED,
  ALLOWED_PAYHEAD_CATEGORY_FOR_WAGES,
  HOURLY,
  PAYHEAD_TYPE_ALLOWANCE,
} from '../constants/seafarer-wages';
import { JOINING_ALLOWANCES, JOINING_DEDUCTIONS } from '../constants/pre-joining-details';
import { getCountries } from '../service/reference-service';
import AUTO_CLOSE from '../constants/toast';
import { MODULES } from '../util';

const { Icon } = styleGuide;
const imageController = new ImageController();

const { PARIS_ONE_HOST } = process.env;
const borderNone = { borderBottom: 'none' };
const borderNonePaddingZero = { borderBottom: 'none', padding: '0px' };
const ON_LEAVE_STATUS = 'on leave';
const DEFAULT_DROPDOWN_VALUE = 'Select status';
const DASH_VALUE = '---';
const STATUS_NTBE = 'ntbe';
const APPT_LETTER_RESERVED_SPACE = '_________________________'; // reserved space for generating appt letter

const convertToArray = (obj) => {
  if (typeof obj === 'object' && obj !== null) {
    Object.keys(obj).forEach((key) => {
      const value = obj[key];
      if (typeof value === 'object') {
        if (Array.isArray(value)) {
          value.forEach((item) => convertToArray(item));
        } else {
          obj[key] = [value];
          convertToArray(value);
        }
      }
    });
  }
};

export const createErrorObject = (arrayItem) => {
  const arr = arrayItem.inner;
  const result = {};
  arr.forEach((obj) => {
    let path = obj.params.path.split('.');
    path = path.map((val) => val.replace('[0]', ''));
    const value = obj.message;

    let objRef = result;
    for (let i = 0; i < path.length; i++) {
      const key = path[i];
      if (!objRef[key]) {
        objRef[key] = i === path.length - 1 ? value : {};
      }
      objRef = objRef[key];
    }

    return result;
  });
  convertToArray(result.seafarer_person);
  return result;
};

const CreatedByLabel = ({ action = 'Created', name, date }) => {
  let label = action;
  label += name ? ` by ${name} ` : ' ';
  if (date) {
    label += `on ${moment(date).format('DD MMM YYYY')}`;
  }

  return <p className="details_page__edited_label">{label}</p>;
};

const PhotoGallery = ({ data, seafarer, setError, seafarerId }) => {
  const [photo, setPhoto] = useState(null);
  const photos = () => {
    const person = seafarer.seafarer_person ?? {};
    return person.photos ?? [];
  };

  useEffect(() => {
    (async () => {
      try {
        // 1 seafarer
        if (data && data.length > 0) {
          const img = await imageController.downloadSeafarerImage(data[0]);
          const base64 = imageController.arrayBufferToBase64(img);
          setPhoto(base64);
        }
      } catch (error) {
        setError('Oops, something went wrong. Please try again.');
        console.error(`Get seafarer by ID: ${seafarerId} failed. Error: ${error}`);
      }
    })();
  }, []);
  return (
    <Carousel interval={null} indicators={false}>
      {photos() && photos().length > 0 ? (
        photos()
          .slice()
          .sort((a, b) => a.order - b.order)
          .map((p, pIdx) => (
            <Carousel.Item key={p?.id}>
              {seafarer.status == 'draft' && (
                <div className="carousel-wrapper">
                  <Icon icon="vessel-draft" className="vessel_image_draft" />
                </div>
              )}
              <img
                className="d-block w-100 rounded"
                height="280"
                style={{ objectFit: 'contain' }}
                src={`data:image/png;base64, ${photo}`}
              />
            </Carousel.Item>
          ))
      ) : (
        <Carousel.Item>
          <div
            className="carousel-wrapper rounded"
            style={{ background: '#F8F9FA', width: '100%', height: 280 }}
          >
            {seafarer.status == 'draft' ? (
              <Icon icon="vessel-draft" size={100} className="vessel_image" />
            ) : (
              <Icon icon="photo" size={100} className="vessel_image" />
            )}
          </div>
        </Carousel.Item>
      )}
    </Carousel>
  );
};

const TableDoubleSectionComponent = ({
  sections,
  title,
  id,
  morePassportsNumber,
  moreSeamanBooksNumber,
  handleDocumentsButton,
  downloadFile,
}) => {
  return (
    <Row>
      <Col>
        <table className="table table-hover">
          <thead className="details_page__table_head">
            <tr>
              <th id={id} colSpan="2">
                {title}
              </th>
            </tr>
          </thead>
          {(sections ?? []).map((elements, index) => {
            let firstItem;
            const items = elements;
            if (items.length > 0) {
              firstItem = items.shift();
            }

            let moreTitle;
            if (index === 0 && morePassportsNumber) {
              moreTitle = `${morePassportsNumber} more Passports`;
            }

            if (index === 1 && moreSeamanBooksNumber) {
              moreTitle = `${moreSeamanBooksNumber} more Seaman’s Books`;
            }

            return (
              <tbody key={uuid()}>
                <TableHeaderRow label={firstItem.label} value={firstItem.value} />
                <TableRow data={items ?? []} downloadFile={downloadFile} />
                <tr>
                  <td colSpan="2" style={borderNonePaddingZero}>
                    <div className="more-documents-wrapper">
                      <Button
                        variant="link"
                        className="selected-file-link"
                        onClick={handleDocumentsButton}
                      >
                        {moreTitle}
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            );
          })}
        </table>
      </Col>
    </Row>
  );
};

const TableRow = ({ data, isSectionHeader, downloadFile }) => {
  return data.map((item, index) => {
    const classLabel = isSectionHeader
      ? 'details_page__row-name__header'
      : 'details_page__row-name';
    const classValue = isSectionHeader
      ? 'details_page__row-value__header'
      : 'details_page__row-value';
    const isDocument = item.document !== undefined;
    const { documentType } = item;

    return (
      <tr key={item.label}>
        <td id={item.key} className={classLabel}>
          {item.label}
        </td>
        <td
          id={`${item.key}-value`}
          className={classValue}
          data-testid={`fml-seafarer-general-details-${index}`}
        >
          {isDocument ? (
            <Button
              variant="link"
              className="selected-file-link"
              onClick={() => downloadFile(documentType, item.document, item.value)}
            >
              {item.value}
            </Button>
          ) : (
            item.value
          )}
        </td>
      </tr>
    );
  });
};

const TableSection = forwardRef((props, ref) => (
  <Row ref={ref}>
    <Col>
      <table style={{ backgroundColor: props.backgroundColor }} className="table table-hover">
        <thead className="details_page__table_head">
          <tr>
            <th id={props.id} colSpan="2">
              {props.title}
            </th>
          </tr>
        </thead>
        <tbody>
          <TableRow data={props.data} downloadFile={props.downloadFile} />
        </tbody>
        {props.nextOfKinSection && (
          <tbody>
            <tr>
              <td colSpan="2" style={borderNone} />
            </tr>
          </tbody>
        )}
        {props.nextOfKinSection || null}
      </table>
    </Col>
  </Row>
));

const RenderSeafarer = ({
  seafarer,
  roleConfig,
  seafarerId,
  setError,
  isLoading,
  setIsLoading,
  setShowDuplicateHKIDModal,
  setShowParentErrorModal,
  history,
  ga4react,
  hasStatusChanged,
  setHasStatusChanged,
  refreshDetailsPageData,
  activeVesselData,
  unableToRecommendErrors,
  screeningInfo,
  filteredHistory,
  filteredHistoryUpdated,
  suptAppraisalData,
  setHasHistoryChanged,
  user,
}) => {
  const seafarerPersonId = seafarer?.seafarer_person_id;
  const [isInviteUserModalVisible, setIsInviteUserModalVisible] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState('');
  const [showUnableToRecommendModal, setShowUnableToRecommendModal] = useState(false);

  const handleOpenInviteUserModal = () => {
    eventTracker('addUserAccount', 'Add user account');
    setIsInviteUserModalVisible(true);
  };
  const handleCloseInviteUserModal = () => setIsInviteUserModalVisible(false);

  const [isInvitationConfirmationModalVisible, setIsInvitationConfirmationModalVisible] =
    useState(false);
  const [isCreatingAccount, setIsCreatingAccount] = useState(false);
  const [errorOnCreateUser, setErrorOnCreateUser] = useState(null);

  const handleClearUserError = () => setErrorOnCreateUser(null);

  const handleOpenInvitationConfirmation = () => {
    eventTracker('submitAddAccount', 'Submit Add account');
    setIsInvitationConfirmationModalVisible(true);
  };
  const handleCloseInvitationConfirmation = () => setIsInvitationConfirmationModalVisible(false);

  // Remove File key
  const cloneSeafarerSchema = cloneDeep(seafarerSchema);
  cloneSeafarerSchema.fields.seafarer_person.fields.seaman_books = getSeamanBooksSchema(
    false,
    true,
  );
  delete cloneSeafarerSchema.fields.seafarer_person.fields.passports.innerType.fields.file;
  delete cloneSeafarerSchema.fields.seafarer_person.fields.seaman_books.innerType.fields.file;

  const controller = new AddSeafarerController();
  const person = seafarer?.seafarer_person ?? {};
  const passport = person.passports && person.passports.length > 0 ? person.passports[0] : {};
  const morePassportsNumber =
    person.passports && person.passports.length > 1 ? person.passports.length - 1 : undefined;
  const seaman_book =
    person.seaman_books && person.seaman_books.length > 0 ? person.seaman_books[0] : {};
  const moreSeamanBooksNumber =
    person.seaman_books && person.seaman_books.length > 1
      ? person.seaman_books.length - 1
      : undefined;
  const seafarerContactDetailsHidden = seafarer.id && !seafarer.can_view_contacts;
  const nextOfKins =
    (person.family_members ?? []).map((kin) =>
      SeafarerNextOfKin(kin, seafarerContactDetailsHidden),
    ) || [];
  const sections = [SeafarerPassport(passport), SeafarerSeamanBook(seaman_book)];

  const firstName = person.first_name ?? '';
  const lastName = person.last_name ?? '';
  const middleName = person.middle_name ?? '';
  const fullName = capitalizeArgs(firstName, middleName, lastName) || Dash;

  const rank = seafarer.seafarer_rank || {};
  const rankTitle = rank?.unit ? `(${rank?.unit})` : Dash;
  const hkid = seafarer.hkid ? `(${seafarer.hkid})` : Dash;

  const headingTitle = `${fullName} ${rankTitle} ${hkid} `;

  const seafarer_contacts = person.seafarer_contacts ?? [];

  const emails =
    useMemo(() => {
      const emails = seafarer_contacts.filter(filterByContactType(EMAIL));
      return emails.map((email) => ({ id: email.contact, value: email.contact }));
    }, [seafarer_contacts]) || [];

  const createdBy = person.created_by_user_info ?? '';
  const createdAt = stringAsDate(person.created_at);
  const updatedBy = person.updated_by_user_info ?? '';
  const updatedAt = stringAsDate(person.updated_at);

  const generalRef = useRef(null);
  const particularsRef = useRef(null);
  const contactRef = useRef(null);
  const documentRef = useRef(null);
  const journeyStatusRemarksRef = useRef(null);
  const examStatusRemarksRef = useRef(null);

  const [currentStatus, setCurrentStatus] = useState({
    journeyStatus: DASH_VALUE,
    examinationStatus: DASH_VALUE,
  });
  const [journeyStatus, setJourneyStatus] = useState(null);
  const [examinationStatus, setExaminationStatus] = useState(null);
  const [journeyStatusDefault, setJourneyStatusDefault] = useState({
    name: 'Select status',
    value: '',
  });
  const [examinationStatusDefault, setExaminationStatusDefault] = useState({
    name: 'Select status',
    value: '',
  });
  const { step = 'general' } = useParams();
  const [showModalPop, setShowModalPop] = useState(false);
  const [errorList, setErrorList] = useState(null);
  const [activeTab, setActiveTab] = useState(step);
  const [childHKID, setChildHKID] = useState(null);
  const [journeyRemarkRequired, setJourneyRemarkRequired] = useState(null);
  const [journeyStatusState, setJourneyStatusState] = useState(null);
  const [dropdownData, setDropdownData] = useState({});
  const [currentSeafarerHistoryStatus, setCurrentSeafarerHistoryStatus] = useState([]);
  const [isEditPreJoiningEnabled, setIsEditPreJoiningEnabled] = useState(false);
  const [isEnableGenerateAppointmentLetter, setIsEnableGenerateAppointmentLetter] = useState(false);

  // seafarer document
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [showUpdateWagesModal, setShowUpdateWagesModal] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [showTravelModal, setShowTravelModal] = useState(false);
  const [emailDataStartTimer, setEmailDataStartTimer] = useState(0);

  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, typeof label === 'string' ? label : toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (showTravelModal) {
      setEmailDataStartTimer(new Date().getTime());
    } else if (emailDataStartTimer) {
      const currentTime = new Date().getTime();
      const elapsedTime = currentTime - emailDataStartTimer;
      ga4EventTrigger(
        'Time Spent Modal',
        'Seafarer Details Page - Send Email Modal',
        moment.utc(elapsedTime).format('mm:ss').toString(),
      );
      setEmailDataStartTimer(0);
    }
  }, [showTravelModal]);

  const setSelectedItem = (inputArray, filterItem, setFunction) => {
    if (Array.isArray(inputArray)) {
      const selectItem = inputArray.filter((item) => item[0] == filterItem);
      if (Array.isArray(selectItem) && selectItem.length > 0) {
        setJourneyRemarkRequired(selectItem[0][1].mandatory_remarks);
        setFunction({ name: selectItem[0][1].name, value: selectItem[0][0] });
        if (selectItem[0][0] == seafarerStatus.ON_LEAVE) {
          setExaminationStatusDefault({
            name: 'No Examination',
            value: seafarerStatus.NO_EXAMINATION,
          });
        }
      }
    }
  };

  const changeJourneyStatus = (event) => {
    setSelectedItem(journeyStatus, event, setJourneyStatusDefault);
  };

  const changeExaminationStatus = (event) => {
    setSelectedItem(examinationStatus, event, setExaminationStatusDefault);
  };

  const handleSeafarerStateChange = async () => {
    const payload = {
      seafarer_journey_status: {
        status: journeyStatusDefault.value,
        remarks: journeyStatusRemarksRef.current?.value ?? null,
      },
    };

    if (journeyStatusDefault.value === seafarerStatus.ON_LEAVE && examinationStatusDefault.value) {
      payload.seafarer_journey_status.seafarer_exam_status = {
        status: examinationStatusDefault.value,
        remarks: examStatusRemarksRef.current?.value ?? null,
      };
    }
    try {
      setShowModalPop(false);
      setError(null);
      setIsLoading(true);
      await seafarerService.updateSeafarerStatus(seafarer?.seafarer_person?.id, payload);
      setHasStatusChanged(Math.random());
      setIsLoading(false);
    } catch (error) {
      setError(
        `Failed to update Seafarer status. Error: ${error.response?.data ?? error.toString()}`,
      );
      setIsLoading(false);
    }
  };

  const handleTabSelect = (key) => {
    eventTracker('tabNavigation', key);

    if (key !== activeTab) {
      if (key === 'screening') {
        setStartTime(moment().valueOf());
      }
      if (activeTab === 'screening') {
        const endTime = moment().valueOf();
        const spentTime = moment.utc(endTime - startTime).format('HH:mm:ss');
        ga4EventTrigger('Time Spent', 'Seafarer Screening', `Seafarer Screening - ${spentTime}`);
        setStartTime(null);
      }
      setActiveTab('');
      setActiveTab(key);
    }

    return history.push(`/seafarer/details/${seafarerId}/${key}`);
  };

  // Adding
  const visitUpdateVessel = () => {
    eventTracker('routeToEdit', 'Edit Seafarer');

    if (activeTab === 'general') {
      history.push(`/seafarer/${seafarerId}/add/basic`);
    } else if (activeTab === 'experience') {
      history.push(`/seafarer/${seafarerId}/add/experience`);
    }
  };
  const visitBankAccountEditForm = () => {
    history.push(`/seafarer/details/${seafarerId}/edit-bank-accounts`);
  };

  const visitUpdatePrejoining = () => {
    eventTracker('editPreJoiningButton', '');
    history.push(`/seafarer/details/${seafarerId}/pre-joining/add`);
  };

  const handleScreeningHistoryButton = () => {
    eventTracker('routeToScreening', 'Screening History');
    history.push(`/seafarer/screening-history/${seafarerId}`);
  };

  const handleDocumentsButton = () => {
    eventTracker('routeToDocuments', 'Documents');
    handleTabSelect('id-documents');
  };

  const visitScreeningPage = () => {
    history.push(`/seafarer/details/${seafarerId}/screening`);
  };

  const handleUpdateWagesButton = () => {
    eventTracker('updateWagesFromActionButton', '');
    history.push(`/seafarer/details/${seafarerId}/pre-joining/wages/add`);
  };

  const handleGenerateAppointmentLetterButton = async () => {
    toast.info('Appointment Letter will be downloaded shortly.');
    try {
      const [
        { displayPayheads, wages },
        preJoiningData,
        { data: vesselData },
        officeDetails,
        { countries: countryList },
      ] = await Promise.all([
        fetchSeafarerWages(),
        fetchPreJoining(),
        vesselService.getVesselV2OwnershipById(currentSeafarerHistoryStatus.vessel_ownership_id),
        seafarerService.getShipPartyById(seafarer?.seafarer_reporting_office?.ship_party_id),
        getCountries(),
      ]);
      if (
        officeDetails?.ship_party_branch_office &&
        !officeDetails?.ship_party_branch_office.can_issue_appointment_letter
      ) {
        throw Error('The Branch Office cannot generate Appointment Letter');
      }
      if (
        officeDetails?.ship_party_manning_agency &&
        !officeDetails?.ship_party_manning_agency.can_issue_appointment_letter
      ) {
        throw Error('The Manning Agency cannot generate Appointment Letter');
      }

      const [ownerData, vesselManager] = await Promise.all([
        vesselData?.owner?.ship_party_id
          ? seafarerService.getShipPartyById(vesselData?.owner?.ship_party_id)
          : Promise.resolve(null),
        vesselData?.vessel?.misc_manager?.ref_id
          ? seafarerService.getShipPartyVesselManagerByRefId(vesselData?.vessel?.misc_manager?.ref_id)
          : Promise.resolve(null),
      ]);

      eventTracker('generateAppointmentLetter', '');
      generateAppointmentLetter(
        displayPayheads,
        wages,
        preJoiningData,
        vesselData,
        ownerData,
        officeDetails,
        vesselManager,
        countryList,
      );
      toast.dismiss(); // dismiss the info toast
      toast.success('Appointment Letter generated successfully.', AUTO_CLOSE);
    } catch (error) {
      console.error('Error in Appointment Letter. ', error);
      toast.dismiss(); // dismiss the info toast
      toast.error(`Error in Generating Appointment Letter. ${error}`, AUTO_CLOSE);
    }
  };

  const validateData = (data) => {
    const cloneObj = _.cloneDeep(data);
    delete cloneObj.remuneration;
    delete cloneObj.earnings;
    delete cloneObj.deductions;
    delete cloneObj.seafarer.nextOfKin;
    delete cloneObj.vesselManager;
    delete cloneObj.reportingOffice;

    const errors = [];
    if (cloneObj.seafarer.isFilipino) {
      const monthlyAllotment = Number.parseFloat(cloneObj.homeAllotment?.monthlyAllotment);
      if (Number.isNaN(monthlyAllotment) || !monthlyAllotment)
        errors.push('Monthly Allotment (Mandatory for Filipino)');
    }
    delete cloneObj.homeAllotment;

    function checkFields(obj: any, parentField: string = '', errorStr: string[] = []): string[] {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];
          const currentField = parentField ? `${parentField}.${key}` : key;
          if (value === null || value === undefined) {
            errorStr.push(`${parentField} ${key}`);
          } else if (typeof value === 'object' && !Array.isArray(value)) {
            checkFields(value, currentField, errorStr);
          }
        }
      }
      return errorStr;
    }

    return checkFields(cloneObj, '', errors);
  };

  const generateAppointmentLetter = (
    payheads,
    seafarerWages,
    preJoiningData,
    vesselData,
    ownerData,
    officeDetails,
    vesselManager,
    countryList,
  ) => {
    const allowance = payheads?.filter((item) => item?.type === PAYHEAD_TYPE_ALLOWANCE);
    const remuneration =
      (seafarerWages &&
        allowance?.map(({ id, head_name, display_order, nature }) => ({
          desc: head_name,
          currency: 'USD',
          amount: seafarerWages?.[`payhead_${id}`],
          order: display_order,
          nature,
        }))) ||
      [];
    remuneration.push({
      desc: 'Total',
      currency: 'USD',
      amount: seafarerWages?.amount_total,
      order: 999,
    });

    const earnings = _.get(preJoiningData, 'seafarer_joining_spendings', [])
      .filter((item) => item.payhead.category === JOINING_ALLOWANCES)
      .map((data) => {
        const { amount_unit, amount, payhead } = data;
        return {
          desc: payhead.head_name,
          currency: amount_unit ?? 'USD',
          amount: amount ?? 0,
          order: payhead.display_order,
        };
      });
    const deductions = _.get(preJoiningData, 'seafarer_joining_spendings', [])
      .filter((item) => item.payhead.category === JOINING_DEDUCTIONS)
      .map((data) => {
        const { amount_unit, amount, payhead } = data;
        return {
          desc: payhead.head_name,
          currency: amount_unit ?? 'USD',
          amount: amount ?? 0,
          order: payhead.display_order,
        };
      });

    const { seafarer_person, seafarer_rank } = seafarer;
    const { family_members, seafarer_status_history } = seafarer_person;
    const { owner, registered_owner, vessel } = vesselData;
    const seafarerCurrentJourneyStatus = seafarer_status_history?.find(
      (status) => status.is_current_status === true,
    );
    const nextOfKin = (family_members ?? []).map((member) => {
      return {
        name: member?.name ?? '',
        surname: member?.surname ?? '',
        relationship: member?.relationship ?? '',
        telephone: member?.telephone ?? '',
        mobilePhone: member?.mobilephone ?? '',
        address: member?.address ? getAddress(member?.address) : '',
        percentage: member?.percentage ?? '',
      };
    });

    const constructOfficeDetails = (officeDetails, countryList) => {
      const officeDatas =
        officeDetails?.ship_party_branch_office || officeDetails?.ship_party_manning_agency;
      const authorizedRepresentativeUserInfo =
        officeDetails?.ship_party_branch_office?.authorized_representative;
      const authorizedRepresentativeTitle = officeDetails?.ship_party_branch_office?.title;
      const authorizedRepresentativeFullName = `${authorizedRepresentativeUserInfo?.first_name ?? ''} ${authorizedRepresentativeUserInfo?.last_name ?? ''}`
      const authorizedRepresentative = authorizedRepresentativeTitle ? `${authorizedRepresentativeTitle} ${authorizedRepresentativeFullName}` : authorizedRepresentativeFullName;
      const reportingOfficeCountry = countryList?.find(c => c.alpha2_code === officeDetails?.iso_country_code) ?? '';

      return {
        id: officeDetails?.id,
        name: officeDetails?.name,
        shipPartyType: officeDetails?.ship_party_type_id,
        address: officeDetails?.house_detail,
        country: reportingOfficeCountry?.value ?? '',
        reportingOfficeName: officeDatas?.reporting_office_name,
        authorizedRepresentativeRank:
          (officeDetails?.ship_party_branch_office
            ? authorizedRepresentativeUserInfo?.rank
            : officeDetails?.ship_party_manning_agency?.title) ?? '',
        authorizedRepresentative: officeDetails?.ship_party_branch_office
          ? authorizedRepresentative
          : officeDatas?.authorized_representative ?? '',
        principalCompany: officeDatas?.principal_company?.name ?? '',
      };
    };

    const vesselManagerAddress = vesselManager
      ? getAddress(
      {
        address1: vesselManager?.state_detail,
        address2: vesselManager?.city_detail,
        address3: vesselManager?.building_detail,
        address4: vesselManager?.house_detail,
        postal_zip_code: vesselManager?.postal_zip_code,
        country: countryList?.find(c => c.alpha2_code === vesselManager?.iso_country_code)?.value || '',
      },
      ', ',
    ) : APPT_LETTER_RESERVED_SPACE;

    const data = {
      seafarer: {
        hkid: `${seafarer.hkid}`,
        fullName,
        seamanBook: seafarer_person.seaman_books?.find((book) => book.is_original)?.number ?? '',
        passport: seafarer_person.passports?.[0]?.number,
        dateOfBirth: seafarer_person.date_of_birth,
        placeOfBirth: seafarer_person.place_of_birth,
        countryOfBirth: seafarer_person.country_of_birth?.value,
        isFilipino: seafarer_person.nationality?.id === 181, //Philippine, Filipino
        nextOfKin,
        maritalStatus: seafarer_person.marital_status ?? '',
        spouseSurname: seafarer_person.surname_of_spouse ?? '',
        spouseName: seafarer_person.name_of_spouse ?? '',
        child: seafarer_person.children_names ?? '',
      },
      seafarer_status_history: {
        vessel: seafarerCurrentJourneyStatus?.vessel_name,
        rank: `${seafarer_rank?.value} (${seafarer_rank?.unit})`,
        repatriationPort: seafarerCurrentJourneyStatus?.repatriation_port ?? '',
        expectedStartDate: seafarerCurrentJourneyStatus?.expected_contract_start_date ?? '',
        expectedEndDate: seafarerCurrentJourneyStatus?.expected_contract_end_date ?? '',
      },
      homeAllotment: {
        currency: preJoiningData?.seafarer_allotment?.allotment_unit,
        firstAllotment: preJoiningData?.seafarer_allotment?.first_allotment,
        monthlyAllotment: preJoiningData?.seafarer_allotment?.monthly_allotment,
      },
      remuneration,
      earnings,
      deductions,
      owner: {
        id: owner?.ship_party_id,
        name: owner?.value,
        isZeroAlcohol: ownerData?.ship_party_owner?.is_zero_alcohal_policy,
      },
      registeredOwner: {
        id: registered_owner?.ship_party_id ?? 0,
        name: registered_owner?.value ?? '', // registered owner might be blank for draft vessel
      },
      vesselManager: {
        name: vesselManager?.name ?? APPT_LETTER_RESERVED_SPACE,
        address: vesselManagerAddress,
      },
      reportingOffice: constructOfficeDetails(officeDetails, countryList),
      vessel: {
        imoNumber: vessel.imo_number,
      },
    };
    console.log('Appt Letter data: ', data);
    const validationErrors = validateData(data);
    if (validationErrors.length > 0) {
      console.error('Validation errors:', validationErrors);
      throw new Error(`Please update the ${validationErrors.join(', ')} field`);
    }
    apptLetterService.generate(data);
  };

  const canDisableChangeStatusBtn = () => {
    const person = seafarer?.seafarer_person;
    return (
      seafarer?.seafarer_person?.screening_status === screeningStatus.REJECTED ||
      (person?.current_account_status === seafarerStatus.INITIALIZED &&
        person?.current_journey_status === seafarerStatus.NEW_APPLICANT)
    );
  };

  const downloadFile = async (documentType, document, fileName) => {
    if (isLoading) {
      return;
    }
    window.open(
      `https://${window.location.hostname}/seafarer/document/${document.id}/${documentType}`,
    );
    // Commented as part of P2-2856 view document
    // await imageController.downloadFile(documentType, document, fileName);
  };

  const handleMarkDuplicateButton = () => {
    eventTracker('markAsDuplicate', 'Mark Duplicate');
    if (seafarer.is_parent) setShowParentErrorModal(true);
    else setShowDuplicateHKIDModal(true);
  };

  const fetchSeafarerWages = async () => {
    try {
      const { data: allWagesData } = await seafarerService.getSeafarerWages(
        seafarer?.id,
        '?with_history=true&disable_email_decryption=true',
      );
      if (allWagesData) {
        const currentPendingWagesData = allWagesData.find(
          (i) => i?.is_history === false && i.status === WAGES_STATUS_PENDING,
        );
        const currentAppliedWagesData = allWagesData.find(
          (i) => i?.is_history === false && i.status === WAGES_STATUS_APPLIED,
        );

        const targetWagesData =
          currentPendingWagesData ?? currentAppliedWagesData ?? _.get(allWagesData, 0);
        let payheads;
        if (targetWagesData && Object.hasOwn(targetWagesData, 'seafarer_wages_details')) {
          const wagesDetailsData = targetWagesData?.seafarer_wages_details;
          payheads = wagesDetailsData.map((e) => e.payhead);
        } else {
          payheads = targetWagesData?.seafarer_wages_details_payheads;
        }
        const displayPayheads = payheads
          ?.filter((e) => ALLOWED_PAYHEAD_CATEGORY_FOR_WAGES.includes(e.category))
          ?.map((i) => {
              if (i.nature === HOURLY) {
                return { ...i, head_name: `${i.head_name} (${HOURLY})` };
              }
              return i;
            });
        displayPayheads.sort(
          (a, b) => a.display_order - b.display_order || a.head_name.localeCompare(b.head_name),
        );
        const wages = extractDataFromResponse(targetWagesData, displayPayheads);
        return { displayPayheads, wages };
      }
    } catch (error) {
      console.log('Failed to load default data', error);
    }
  };

  const extractDataFromResponse = (targetWagesData, displayPayheads) => {
    let wagesData = targetWagesData;
    wagesData = {
      ...wagesData,
      seafarer_status_history: getStatusHistoryWithLatestVesselOwnershipData(
        activeVesselData,
        wagesData?.seafarer_status_history,
      ),
    };
    if (wagesData.status === WAGES_STATUS_APPLIED)
      wagesData = { ...wagesData, seafarer_promotion: [] };
    const isMissingWagesDetails = !!wagesData.seafarer_wages_details_payheads;

    if (!isMissingWagesDetails) {
      const res = displayPayheads.map((e) => {
        const keyName = `payhead_${e.id}`;
        const wageDetail = wagesData.seafarer_wages_details.find((ele) => ele.payhead_id === e.id);
        const nullSalary = 0;
        const amt = wageDetail?.amount ? wageDetail?.amount : nullSalary;
        const result = [keyName.toString(), twoDigitFloat(amt)];
        return result;
      });
      const payheads = {
        ...Object.fromEntries(res),
        amount_total: targetWagesData.amount_total,
      };
      return payheads;
    } else {
      const res = displayPayheads.map((e) => {
        const keyName = `payhead_${e.id}`;
        const result = [keyName.toString(), twoDigitFloat(e.default_value)];
        return result;
      });
      const payheads = {
        ...Object.fromEntries(res),
        amount_total: targetWagesData.amount_total,
      };
      return payheads;
    }
  };

  const fetchPreJoining = async () => {
    try {
      const { data } = await seafarerService.getSeafarerPreJoiningDetails(seafarer?.id, {
        decrypt_user_hash: false,
      });
      return data;
    } catch (error) {
      console.log('Failed to load default data', error);
    }
  };

  useEffect(() => {
    (async () => {
      try {
        setChildHKID(null);
        setErrorList(null);
        const dropDownDataResponse = await controller.loadDropDownData();
        const manningAgentDropDownData = await controller.loadManningAgentDropDownData();

        const _dropDownData = {
          ...dropDownDataResponse,
          manningAgents: manningAgentDropDownData,
        };

        setDropdownData(_dropDownData);
        const currentSeafarerStatusHistoryResponse = await seafarerService.getSeafarerStatus(
          `${seafarerPersonId}?is_current_status=true`,
        );
        setCurrentSeafarerHistoryStatus(
          getStatusHistoryWithLatestVesselOwnershipData(
            activeVesselData,
            currentSeafarerStatusHistoryResponse.data[0],
          ),
        );

        // Error List generation;
        try {
          await cloneSeafarerSchema.validate(seafarer, {
            abortEarly: false,
            context: { dropDownData: dropDownDataResponse, value: seafarer },
          });
        } catch (error) {
          if (error.errors) {
            setErrorList(createErrorObject(error));
          }
        }
        // Child HKID
        const childHKIDResponse = await seafarerService.getChildHKID(seafarerId);
        setChildHKID(childHKIDResponse.data);
        // Get Journey and Examination Status object
        const journeyStatus = seafarerStatusService.getActiveJourneyStatus();
        const examinationStatus = seafarerStatusService.getExamStatus();
        // Convert object into JSON
        const journeyStatusJSON = Object.entries(journeyStatus);
        const examinationStatusJSON = Object.entries(examinationStatus);
        // Get Next Available Status for Current Status
        const nextStatus =
          person?.current_journey_status &&
          journeyStatus[person?.current_journey_status]?.next_possible_status;
        // Insert Third Item for enable/disable dropdown item
        const journeyStatusWithEnable = journeyStatusJSON.map((item) => {
          if (nextStatus && !item[1].is_crew_assignment_related && nextStatus.includes(item[0])) {
            switch (item[0]) {
              case 'on_leave':
                if (
                  person?.current_journey_status == STATUS_NTBE &&
                  roleConfig?.seafarer?.edit?.statusBlacklistedToOnLeave === false
                ) {
                  item.push(false);
                } else item.push(true);
                break;
              case 'ntbe':
                if (roleConfig?.seafarer?.edit?.statusBlacklist === false) {
                  item.push(false);
                } else item.push(true);
                break;
              default:
                item.push(true);
            }
            return item;
          }
          return item;
        });

        // Get Next Available Status for Examination Status
        const nextExamStatus =
          person?.current_exam_status &&
          examinationStatus[person?.current_exam_status]?.next_possible_status;
        // Insert Third Item for enable/disable dropdown item
        const examStatusWithEnable = examinationStatusJSON.map((item) => {
          if (nextExamStatus?.includes(item[0])) {
            item.push(true);
            return item;
          }
          item.push(false);
          return item;
        });
        setJourneyStatus(journeyStatusWithEnable);
        setExaminationStatus(examStatusWithEnable);
        const status = {
          journeyStatus: await getCurrentStatus(person?.current_journey_status),
          examinationStatus: await getCurrentStatus(person?.current_exam_status),
        };
        setCurrentStatus({ ...status });

        if (person?.current_journey_status) {
          setSelectedItem(
            journeyStatusWithEnable,
            person?.current_journey_status,
            setJourneyStatusDefault,
          );
          const isEnableGeneratingAppointmentLetter =
            roleConfig?.seafarer?.generate?.appointmentLetter &&
            ALLOWED_APPOINTMENT_LETTER_STATUSES.includes(person?.current_journey_status);
          setIsEnableGenerateAppointmentLetter(isEnableGeneratingAppointmentLetter);
        }

        if (person?.current_exam_status) {
          setSelectedItem(
            examinationStatusJSON,
            person?.current_exam_status,
            setExaminationStatusDefault,
          );
        }
      } catch (error) {
        setError(`Oops, something went wrong. Please try again. Error: ${error}`);
      }
    })();
  }, [hasStatusChanged, seafarer]);

  const eventTracker = (type, value) => {
    switch (type) {
      case 'tabNavigation':
        if (value === 'id-documents') {
          ga4EventTrigger('View', 'ID Document', 'Seafarer Details');
        } else if (value === 'endorsement') {
          ga4EventTrigger('View', 'Endorsement Verification', 'Seafarer Details');
        } else if (value === 'other-documents') {
          ga4EventTrigger('View', 'Other Document', 'Seafarer Details');
        } else if (value === 'experience') {
          ga4EventTrigger('View', 'Seafarer Experience', 'Seafarer Details');
        } else if (value === 'availability') {
          ga4EventTrigger('View', 'Seafarer Availability', 'Seafarer Details');
        } else if (value === 'pre-joining') {
          ga4EventTrigger('View', 'Seafarer Pre Joining', toString(seafarer?.hkid));
        } else if (value === 'status-history') {
          ga4EventTrigger('View', 'Seafarer Status History', 'Seafarer Details');
        } else {
          ga4EventTrigger('Tab', 'Nav', 'Seafarer Details - Edit Seafarer');
        }
        break;
      case 'routeToEdit':
        ga4EventTrigger('Open', value, 'Seafarer Details');
        break;
      case 'routeToScreening':
        ga4EventTrigger('Open', value, 'Seafarer Details');
        break;
      case 'routeToDocuments':
        ga4EventTrigger('Open', value, 'Seafarer Details');
        break;
      case 'routeToBankAccounts':
        ga4EventTrigger('Open', value, 'Seafarer Details');
        break;
      case 'seafarerExperience':
        ga4EventTrigger(value, 'Seafarer Experience', 'Seafarer Details');
        break;
      case 'Back':
        ga4EventTrigger('Back', 'Nav', 'Seafarer Details');
        break;
      case 'changeStatus':
        ga4EventTrigger('Change Status', 'Edit Seafarer', 'Seafarer Details');
        break;
      case 'addDocument':
        ga4EventTrigger('Add Document', 'Document', 'Seafarer Details');
        break;
      case 'more':
        ga4EventTrigger('More', 'Menu', 'Seafarer Details');
        break;
      case 'screeningDetails':
        ga4EventTrigger(value, 'Seafarer Screening', 'Seafarer Details');
        break;
      case 'editDocument':
        ga4EventTrigger('Edit Document', 'Document', 'Seafarer Details');
        break;
      case 'deleteDocument':
        ga4EventTrigger('Delete Document', 'Document', 'Seafarer Details');
        break;
      case 'viewDocument':
        ga4EventTrigger('View Document', 'Document', 'Seafarer Details');
        break;
      case 'uploadDocument':
        ga4EventTrigger('Upload Document', 'Document', 'Seafarer Details');
        break;
      case 'routeToCrewList':
        ga4EventTrigger('Crew List from Seafarer', 'Link', 'Seafarer Details');
        break;
      case 'editExperience':
        ga4EventTrigger(value, 'Seafarer Experience', 'Seafarer Details');
        break;
      case 'editAvailability':
        ga4EventTrigger('Edit', 'Seafarer Availability', 'Seafarer Details');
        break;
      case 'travel':
        ga4EventTrigger('Travel', 'Edit Seafarer', 'Seafarer Details');
        break;
      case 'markAsDuplicate':
      case 'needHelp':
      case 'viewDuplicate':
      case 'viewP1':
      case 'addUserAccount':
      case 'submitAddAccount':
        ga4EventTrigger(value, 'Seafarer Details', 'Seafarer Details');
        break;
      case 'updateWagesFromActionButton':
        ga4EventTrigger('Wages Promotion', 'Seafarer - Menu', toString(seafarer?.hkid));
        break;
      case 'generateAppointmentLetter':
        ga4EventTrigger('Generate Appointment Letter', 'Seafarer - Menu', toString(seafarer?.hkid));
        break;
      case 'updateWagesFromGeneralTab':
        ga4EventTrigger(
          'Pending Wages Promotion Update',
          'Seafarer - Menu',
          toString(seafarer?.hkid),
        );
        break;
      case 'cancelWagesFromGeneralTab':
        ga4EventTrigger(
          'Pending Wages Promotion Cancel',
          'Seafarer - Menu',
          toString(seafarer?.hkid),
        );
        break;
      case 'confirmCancelWagesFromGeneralTab':
        ga4EventTrigger(
          'Pending Wages Promotion Cancel Confirm',
          'Seafarer - Menu',
          toString(seafarer?.hkid),
        );
        break;
      case 'pjCheckListLink':
        ga4EventTrigger(
          'P1 PJ Checklist Link',
          'Seafarer Pre Joining - link',
          toString(seafarer?.hkid),
        );
        break;
      case 'editPreJoiningButton':
        ga4EventTrigger(
          'Edit Pre Joining',
          'Seafarer Pre Joining - menu',
          toString(seafarer?.hkid),
        );
        break;
      case 'setWagesButton':
        ga4EventTrigger('Set Wages', 'Seafarer Pre Joining - menu', toString(seafarer?.hkid));
        break;
      case 'updateWagesModalPromotionCheckBox':
        ga4EventTrigger('Promote Seafarer', 'Seafarer Wages Promotion', toString(seafarer?.hkid));
        break;
      case 'updateWagesModalSaveButton':
        ga4EventTrigger(
          'Save Wages Promotion',
          'Seafarer Wages Promotion',
          toString(seafarer?.hkid),
        );
        break;
      case 'updateWagesModalCancelButton':
        ga4EventTrigger(
          'Close Wages Promotion',
          'Seafarer Wages Promotion',
          toString(seafarer?.hkid),
        );
        break;
      case 'experienceTableSalaryLink':
        ga4EventTrigger('Salary History', 'Seafarer Experience', toString(seafarer?.hkid));
        break;
      case 'wagesHistoryModalDropdown':
        ga4EventTrigger('Salary History Dropdown', 'Seafarer Experience', toString(seafarer?.hkid));
        break;
      case 'SeafarerExperienceCurrencyUnit':
        ga4EventTrigger('Currency Unit Change', value, 'Add/Edit Seafarer Experience');
        break;
      case 'setContractDetailsModalSaveButton':
        ga4EventTrigger('Set Contract Details', 'Seafarer Details - Pre-Joining', 'Click');
        break;
      case 'expectedSignedOnBackDate':
        ga4EventTrigger('Backdate Expected Sign-On Date', 'Seafarer Details - Pre-Joining', 'Date Picker');
        break;
      case 'setToTravelModalSaveButton':
        ga4EventTrigger('Set to Travel', 'Seafarer Details - General', value);
        break;
      case 'cancelTravelModalSaveButton':
        ga4EventTrigger('Reject Travelling Plan', 'Seafarer Details - General', 'Click');
        break;
      case 'changePeriodModalSaveButton':
        ga4EventTrigger('Change Period on Board', 'Seafarer Details - General', 'Date Picker');
        break;
      case 'editAllotmentDetailsButton':
        ga4EventTrigger('Edit Allotment Details', 'Seafarer Details - Pre-Joining', 'Click');
        break;
      case 'editJoiningExpenseButton':
        ga4EventTrigger('Edit Joining Expense', 'Seafarer Details - Pre-Joining', 'Edit');
        break;
      case 'addAppointmentLetterModalSaveButton':
        ga4EventTrigger('Add Document', 'Seafarer Details - Other Documents', 'Click');
        break;
      case 'editAppointmentLetterModalSaveButton':
        ga4EventTrigger('Edit User Defined Document', 'Seafarer Details - Other Documents', 'Click');
        break;
      default:
        ga4EventTrigger('Click', 'Seafarer Details', value);
        break;
    }
  };

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
      {
        title: headingTitle ?? '- - -',
        label: 'Details',
        link: '#',
      },
    ],
    [headingTitle],
  );

  const onBreadcrumbClick = (label) => {
    eventTracker('Back', toString(label));
  };

  const canDisableStatusChangeConfirm = () => {
    return (
      !(journeyStatusDefault.name?.toLowerCase()?.toString() === ON_LEAVE_STATUS
        ? !!examinationStatusDefault.value
        : !!journeyStatusDefault.value) ||
      (journeyStatusDefault.name?.toLowerCase()?.toString() === ON_LEAVE_STATUS
        ? currentStatus.examinationStatus === examinationStatusDefault.name
        : currentStatus.journeyStatus === journeyStatusDefault.name) ||
      (journeyRemarkRequired &&
        (journeyStatusState == null || journeyStatusState?.trim()?.length == 0))
    );
  };

  const getCurrentStatus = async (currentStatus) => {
    if (currentStatus) {
      const status = await seafarerStatusService.getStatusByKey(currentStatus);
      return status?.name ?? DASH_VALUE;
    }
    return DASH_VALUE;
  };

  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setJourneyStatusState(value);
  };

  const routeToCrewList = (vesselOwnershipId) => {
    if (vesselOwnershipId) {
      const date = dateAsString(new Date());
      window.open(`/seafarer/crew-list/vessel/${vesselOwnershipId}/${dateAsDash(date)}`, '_blank');
    }
  };

  const routeToVesselPage = (vesselOwnershipId) => {
    if (vesselOwnershipId) {
      history.push(`/vessel/ownership/details/${vesselOwnershipId}`);
    }
  };

  const onRecommendClick = () => {
    if (seafarer.seafarer_person.current_account_status !== 'active') {
      setError('The seafarer is not eligible for the recommendation');
      return;
    }
    const existingRecommendationPlan = filteredHistory.find((plan) =>
      [
        seafarerStatus.RECOMMENDED,
        seafarerStatus.RECOMMENDED_WITH_DEVIATION,
        seafarerStatus.CREW_ASSIGNMENT_APPROVED,
      ].includes(plan.seafarer_journey_status),
    );
    if (existingRecommendationPlan) {
      setError('The seafarer already has existing recommendation');
      return;
    }
    if (!unableToRecommendErrors.length) {
      history.push(`/seafarer/${seafarerId}/recommendation`);
    } else {
      setShowUnableToRecommendModal(true);
    }
  };

  const onPrintBioClick = () => {
    const { BASE_URL } = process.env;
    window.open(`${BASE_URL}/seafarer/${seafarer.seafarer_person_id}/bio-data`, '_blank');
  };

  const handleConfirmAddSeafarerAccount = useCallback(async () => {
    try {
      handleCloseInvitationConfirmation();
      setIsCreatingAccount(true);
      await createKeycloakAccount({
        email: selectedEmail,
        first_name: firstName,
        last_name: lastName,
        attributes: {
          seafarer_id: seafarerId,
          is_user_onboarded: 'false',
        },
        groups: [],
        realmRoles: [],
        sendActionEmail: true,
        syncToPARIS1: true,
      });
    } catch (error) {
      console.log('Something went wrong on creating keycloak account for seafarer:', error);
      if (error?.response?.data) setErrorOnCreateUser(error.response.data);
      else setErrorOnCreateUser('Something went wrong. Please try again.');
    } finally {
      setIsCreatingAccount(false);
    }
  }, [seafarerId, selectedEmail, firstName, lastName]);

  const getScreeningMessage = () => {
    if (seafarer.seafarer_person.screening_status !== screeningStatus.PASSED) {
      const isScreeningRejected =
        seafarer.seafarer_person.screening_status === screeningStatus.REJECTED;
      return (
        <div
          className={
            isScreeningRejected
              ? 'screening-status reject-status'
              : 'screening-status under-screening-status'
          }
        >
          <span>{screeningInfo.message}</span>
        </div>
      );
    }
  };

  return (
    <div className="details_page">
      {getScreeningMessage()}
      {showDocumentModal ? (
        <AddDocumentModal
          setShowDocumentModal={setShowDocumentModal}
          seafarerPersonId={seafarer.seafarer_person_id}
          history={history}
          dropdownData={dropdownData}
          eventTracker={eventTracker}
          onSubmitCallback={refreshDetailsPageData}
        />
      ) : (
        ''
      )}
      {showTravelModal && (
        <TravelModal
          data={{
            seafarers: [
              {
                id: seafarer.id,
                hkid: seafarer.hkid,
                firstName,
                middleName,
                lastName,
              },
            ],
            vesselName: currentSeafarerHistoryStatus[0]?.vessel_name,
          }}
          show={showTravelModal}
          onClose={() => setShowTravelModal(false)}
          onSave={() => setShowTravelModal(false)}
          ga4EventTrigger={ga4EventTrigger}
          user={user}
          calledFromModule={MODULES.DETAILS_PAGE}
        />
      )}
      {showUpdateWagesModal ? (
        <UpdateWagesModal
          setShowUpdateWagesModal={setShowUpdateWagesModal}
          seafarer={seafarer}
          history={history}
          showUpdateWagesModal={showUpdateWagesModal}
          refreshDetailsPageData={refreshDetailsPageData}
          eventTracker={eventTracker}
          roleConfig={roleConfig}
          activeVesselData={activeVesselData}
        />
      ) : (
        ''
      )}
      <InviteUserAccountModal
        isVisible={isInviteUserModalVisible}
        onClose={handleCloseInviteUserModal}
        emails={emails}
        containerStyle={isInvitationConfirmationModalVisible && { opacity: '0.7' }}
        onAddUser={handleOpenInvitationConfirmation}
        seafarerID={seafarerId}
        selectedEmail={selectedEmail}
        setSelectedEmail={setSelectedEmail}
        isCreatingAccount={isCreatingAccount}
      />
      <ConfirmationModal
        isVisible={isInvitationConfirmationModalVisible}
        onClose={handleCloseInvitationConfirmation}
        title="Confirm Adding Account"
        onConfirm={handleConfirmAddSeafarerAccount}
      />
      <ConfirmationModal
        isVisible={!!errorOnCreateUser}
        onClose={handleClearUserError}
        title={errorOnCreateUser}
      />
      <UnableToRecommendModal
        show={showUnableToRecommendModal}
        onConfirm={() => setShowUnableToRecommendModal(false)}
        unableToRecommendErrors={unableToRecommendErrors}
      />
      <Modal
        id="seafarer-status-popup"
        show={showModalPop}
        aria-labelledby="confirmation-modal"
        centered
      >
        <Modal.Header>
          <Modal.Title id="confirmation-modal" style={{ borderBottom: '0' }}>
            Change Status
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Row className="modal-label">Journey Status </Form.Row>
            <Form.Row>
              <DropdownButton
                align="start"
                title={journeyStatusDefault.name}
                id="dropdown-menu-align-end"
                variant="btn-no-variant"
                className="w-100"
                onSelect={changeJourneyStatus}
              >
                <Dropdown.Header className="dropdown-header-text">ACTIVE</Dropdown.Header>
                {journeyStatus?.map((item, index) => {
                  if (item[1]?.account_status == 'active')
                    return (
                      <Dropdown.Item
                        eventKey={item[0]}
                        key={item[0]}
                        className="dropdown-item-text"
                        style={
                          !item[2] || item[1]?.is_crew_assignment_related
                            ? { color: '#c6cdd3', pointerEvents: 'none' }
                            : {}
                        }
                      >
                        {item[1]?.name}
                      </Dropdown.Item>
                    );
                })}
                <Dropdown.Divider />
                <Dropdown.Header className="dropdown-header-text ">INACTIVE</Dropdown.Header>
                {journeyStatus?.map((item, index) => {
                  if (item[1]?.account_status == 'inactive')
                    return (
                      <Dropdown.Item
                        eventKey={item[0]}
                        key={item[0]}
                        className="dropdown-item-text"
                        style={
                          !item[2] || item[1]?.is_crew_assignment_related
                            ? { color: '#c6cdd3', pointerEvents: 'none' }
                            : {}
                        }
                      >
                        {item[1]?.name}
                      </Dropdown.Item>
                    );
                })}
                <Dropdown.Divider />
                <Dropdown.Header className="dropdown-header-text">ARCHIVED</Dropdown.Header>
                {journeyStatus?.map((item, index) => {
                  if (item[1]?.account_status == 'archived')
                    return (
                      <Dropdown.Item
                        eventKey={item[0]}
                        key={item[0]}
                        className="dropdown-item-text"
                        style={
                          !item[2] || item[1]?.is_crew_assignment_related
                            ? { color: '#c6cdd3', pointerEvents: 'none' }
                            : {}
                        }
                      >
                        {item[1]?.name}
                      </Dropdown.Item>
                    );
                })}
              </DropdownButton>
            </Form.Row>
            <Form.Row className="modal-label mt-2">Remarks </Form.Row>
            <Form.Row>
              <Form.Control
                as="textarea"
                rows={3}
                ref={journeyStatusRemarksRef}
                onChange={(e) => handleChange(e)}
              />
            </Form.Row>
            {journeyStatusDefault?.name?.toLowerCase()?.toString() === ON_LEAVE_STATUS && (
              <>
                <Form.Row className="modal-label mt-2">Examination Status </Form.Row>
                <Form.Row>
                  <DropdownButton
                    align="start"
                    title={examinationStatusDefault.name}
                    id="dropdown-menu-align-end"
                    variant="btn-no-variant"
                    className="w-100"
                    onSelect={changeExaminationStatus}
                  >
                    {examinationStatus?.map((item, index) => {
                      return (
                        <Dropdown.Item
                          eventKey={item[0]}
                          key={item[0]}
                          className="dropdown-item-text"
                          style={!item[2] ? { color: '#c6cdd3', pointerEvents: 'none' } : {}}
                        >
                          {item[1]?.name}
                        </Dropdown.Item>
                      );
                    })}
                  </DropdownButton>
                </Form.Row>
                <Form.Row className="modal-label mt-2">Remarks </Form.Row>
                <Form.Row>
                  <Form.Control as="textarea" rows={3} ref={examStatusRemarksRef} />
                </Form.Row>
              </>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer style={{ borderTop: '0' }}>
          <Button
            variant="primary"
            onClick={() => {
              setShowModalPop(false);
              setJourneyStatusState('');
              changeJourneyStatus(person?.current_journey_status);
              changeExaminationStatus(person?.current_exam_status);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            className="confirm-btn"
            onClick={() => {
              handleSeafarerStateChange();
            }}
            disabled={canDisableStatusChangeConfirm()}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
      <Row>
        <Col className="col-md-auto">
          <div className="d-flex flex-row">
            <div className="d-flex align-items-center">
              <BreadcrumbHeader
                items={breadCrumbsItems}
                activeItem={headingTitle}
                onClick={onBreadcrumbClick}
              />
            </div>
          </div>
          <a
            target="_blank"
            href={`${PARIS_ONE_HOST}/fml/PARIS?display=crewoverview&crewid=${seafarer.ref_id}`}
            type="absolute"
            className="paris1-link-detail-page"
            rel="noreferrer"
            onClick={() => {
              eventTracker('viewP1', 'View P1 seafarer');
            }}
          >
            View PARIS 1.0 Detail Page
          </a>
          {childHKID?.length > 0 && (
            <div>
              <DuplicateChildHKIDInfo
                setIsLoading={setIsLoading}
                childHKIDData={childHKID}
                setChildHKID={setChildHKID}
                eventTracker={eventTracker}
              />
            </div>
          )}
        </Col>
        <Col className="col-md-auto seafarer-status-section">
          <div className="d-flex align-items-center seafarer-status">
            {person?.current_account_status?.toUpperCase()
              ? person?.current_account_status?.toUpperCase()
              : DASH_VALUE}
          </div>
          <div
            className={`status-secondary-line status-secondary-${person?.current_account_status?.toLowerCase()}`}
            data-testid="fml-seafarer-journey-status"
          >
            {currentStatus?.journeyStatus}
            {currentStatus?.journeyStatus?.toLowerCase()?.toString() === ON_LEAVE_STATUS && (
              <span>
                {' '}
                <span className="dot-status"> </span>
                {currentStatus?.examinationStatus}
              </span>
            )}
            {currentStatus?.journeyStatus === 'Signed On (Crew Assignment)' && (
              <>
                <span>
                  <Button
                    variant="link"
                    onClick={() =>
                      routeToVesselPage(currentSeafarerHistoryStatus?.vessel_ownership_id)
                    }
                  >
                    {currentSeafarerHistoryStatus?.vessel_name}
                  </Button>
                </span>
                <span>
                  <Button
                    variant="link"
                    onClick={() => {
                      eventTracker('routeToCrewList', 'Seafarer Details');
                      routeToCrewList(currentSeafarerHistoryStatus?.vessel_id);
                    }}
                  >
                    Crew List
                  </Button>
                </span>
              </>
            )}
          </div>
        </Col>
        <Col>
          <ButtonsToolBar
            roleConfig={roleConfig}
            isEditPreJoiningEnabled={isEditPreJoiningEnabled}
            visitScreeningPage={visitScreeningPage}
            visitUpdateVessel={visitUpdateVessel}
            handleDocumentsButton={handleDocumentsButton}
            handleScreeningHistoryButton={handleScreeningHistoryButton}
            handleMarkDuplicateButton={handleMarkDuplicateButton}
            disableChangeStatusButton={canDisableChangeStatusBtn()}
            setModalPopUp={setShowModalPop}
            handleParis2UserAccountButton={handleOpenInviteUserModal}
            seafarer={seafarer}
            setShowDocumentModal={setShowDocumentModal}
            handleUpdateWagesButton={handleUpdateWagesButton}
            isEnableGenerateAppointmentLetter={isEnableGenerateAppointmentLetter}
            handleGenerateAppointmentLetterButton={handleGenerateAppointmentLetterButton}
            eventTracker={eventTracker}
            activeTab={activeTab}
            visitBankAccountEditForm={visitBankAccountEditForm}
            visitUpdatePrejoining={visitUpdatePrejoining}
            setShowTravelModal={setShowTravelModal}
            recommendClickAction={onRecommendClick}
            printBioClickAction={onPrintBioClick}
            enableRecommendationButton={filteredHistoryUpdated}
            ga4EventTrigger={ga4EventTrigger}
          />
        </Col>
      </Row>
      <Tab.Container activeKey={activeTab} defaultActiveKey="all">
        <Row className="no-print mt-4">
          <Col>
            <TabWrapper
              handleTabSelect={handleTabSelect}
              data={DetailPageTabData}
              step={step}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </Col>
        </Row>
      </Tab.Container>
      <Switch>
        <Route exact path="/seafarer/details/:seafarerId/screening">
          <ScreeningPage eventTracker={eventTracker} setChangedStatus={setHasStatusChanged} />
        </Route>

        <Route exact path="/seafarer/details/:seafarerId/status-history">
          <StatusHistoryPage seafarerPersonId={seafarer.seafarer_person_id}
          />
        </Route>

        <Route
          exact
          path={[
            '/seafarer/details/:seafarerId/id-documents',
            '/seafarer/details/:seafarerId/id-documents/:documentType/edit/:documentId',
          ]}
        >
          <DocumentsPage
            seafarer={seafarer}
            dropdownData={dropdownData}
            eventTracker={eventTracker}
          />
        </Route>

        <Route
          exact
          path={[
            '/seafarer/details/:seafarerId/endorsement',
            '/seafarer/details/:seafarerId/endorsement/:documentType/edit/:documentId',
          ]}
        >
          <EndorsementAndVerification
            seafarer={seafarer}
            roleConfig={roleConfig}
            dropdownData={dropdownData}
            eventTracker={eventTracker}
          />
        </Route>

        <Route
          exact
          path={[
            '/seafarer/details/:seafarerId/other-documents',
            '/seafarer/details/:seafarerId/other-documents/:documentType/edit/:documentId',
          ]}
        >
          <OtherDocumentsPage
            seafarer={seafarer}
            seafarerId={seafarerId}
            dropdownData={dropdownData}
            eventTracker={eventTracker}
            refreshDetailsPageData={refreshDetailsPageData}
          />
        </Route>

        <Route exact path="/seafarer/details/:seafarerId/availability">
          <AvailabilityPage
            seafarer={seafarer}
            seafarerId={seafarerId}
            roleConfig={roleConfig}
            eventTracker={eventTracker}
          />
        </Route>
        <Route
          exact
          path={[
            '/seafarer/details/:seafarerId/pre-joining',
            '/seafarer/details/:seafarerId/pre-joining/wages/add',
          ]}
        >
          <PreJoiningTab
            seafarer={seafarer}
            setIsEditPreJoiningEnabled={setIsEditPreJoiningEnabled}
            setShowUpdateWagesModal={setShowUpdateWagesModal}
            eventTracker={eventTracker}
            roleConfig={roleConfig}
            activeVesselData={activeVesselData}
              setHasHistoryChanged={setHasHistoryChanged}
          />
        </Route>
        <Route
          exact
          path={[
            '/seafarer/details/:seafarerId/experience',
            '/seafarer/details/:seafarerId/experience/:experienceId/wages-history',
          ]}
        >
          <SeafarerExperienceSummary
            seafarer={seafarer}
            roleConfig={roleConfig}
            TableSection={TableSection}
            eventTracker={eventTracker}
          />
        </Route>
        <Route
          exact
          path={[
            '/seafarer/details/:seafarerId/appraisals',
            '/seafarer/details/:seafarerId/appraisals/training-req/:trainingReqId/edit',
            '/seafarer/details/:seafarerId/appraisals/investigation-training-req/:trainingReqId/edit',
          ]}
        >
          <AppraisalsPage
            ga4react={ga4react}
            seafarer={seafarer}
            refreshDetailsPageData={refreshDetailsPageData}
          />
        </Route>
        <Route exact path="/seafarer/details/:seafarerId/account-details">
          <AccountDetailsPage roleConfig={roleConfig} />
        </Route>
      </Switch>
      <Route exact path="/seafarer/details/:seafarerId/pre-joining/wages/add">
        <UpdateWagesModal
          setShowUpdateWagesModal={setShowUpdateWagesModal}
          showUpdateWagesModal={showUpdateWagesModal}
          seafarer={seafarer}
          history={history}
          refreshDetailsPageData={refreshDetailsPageData}
          eventTracker={eventTracker}
          roleConfig={roleConfig}
          activeVesselData={activeVesselData}
        />
      </Route>
      <Route exact path="/seafarer/details/:seafarerId/experience/:experienceId/wages-history">
        <WagesHistoryModal seafarer={seafarer} history={history} eventTracker={eventTracker} />
      </Route>
      {activeTab == 'general' && (
        <>
          <Row id="details-page-error">
            <Col>
              {' '}
              {errorList && (
                <ErrorsListComponent
                  errors={errorList}
                  csstyle={{ marginTop: 0 }}
                  liststyle={{ listStyleType: 'disc' }}
                  headertext="Correct the following:"
                  isDisplayLinkType={false}
                />
              )}
            </Col>
          </Row>
          {seafarer?.parent_hkid && (
            <DuplicateSeafarerAlert
              parentHKID={seafarer?.parent_hkid}
              eventTracker={eventTracker}
            />
          )}
          <Row>
            <Col md={6} lg={6} xl={4} xxl={4}>
              <Row>
                <Col className="no-print">
                  <Row>
                    <Col>
                      <PhotoGallery
                        data={person.photos ?? []}
                        seafarer={seafarer}
                        setError={setError}
                        seafarerId={seafarerId}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <TableSection
                        backgroundColor="#F8F9FA"
                        id="general1"
                        title="General Details"
                        data={SeafarerGeneralDetails(seafarer)}
                        downloadFile={downloadFile}
                        ref={generalRef}
                      />
                      {suptAppraisalData?.length ? (
                        <SuptAppraisalSection suptAppraisalData={suptAppraisalData} />
                      ) : null}
                      <Row>
                        <CreatedByLabel name={createdBy} date={createdAt} />
                      </Row>
                      <Row>
                        {updatedBy && (
                          <CreatedByLabel action="Updated" name={updatedBy} date={updatedAt} />
                        )}
                      </Row>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Col>
            <Col md={6} lg={6} xl={4} xxl={4}>
              <ViewCrewAssignmentPlan
                seafarer={seafarer}
                roleConfig={roleConfig}
                eventTracker={eventTracker}
                activeVesselData={activeVesselData}
                setError
                dropdownData={dropdownData}
                setHasStatusChanged={setHasStatusChanged}
                plansList={filteredHistory}
                setHasHistoryChanged={setHasHistoryChanged}
              />
              <Row ref={particularsRef}>
                <Col>
                  <TableSection
                    id="personal_particulars"
                    title="Personal Particulars"
                    data={SeafarerPersonalParticulars(seafarer)}
                    downloadFile={downloadFile}
                    nextOfKinSection={<TableMultipleSection sections={nextOfKins} />}
                  />
                </Col>
              </Row>
            </Col>

            <Col md={6} lg={6} xl={4} xxl={4}>
              <Row ref={contactRef}>
                <Col>
                  {!seafarerContactDetailsHidden && (
                    <ContactDetailsSection
                      contacts={
                        seafarer?.seafarer_person
                          ? seafarer?.seafarer_person?.seafarer_contacts
                          : []
                      }
                      addresses={
                        seafarer?.seafarer_person ? seafarer?.seafarer_person?.addresses : []
                      }
                    />
                  )}
                </Col>
              </Row>

              <Row ref={documentRef}>
                <Col>
                  <TableDoubleSectionComponent
                    id="documents"
                    title="Documents"
                    sections={sections}
                    morePassportsNumber={morePassportsNumber}
                    moreSeamanBooksNumber={moreSeamanBooksNumber}
                    handleDocumentsButton={handleDocumentsButton}
                    downloadFile={downloadFile}
                  />
                </Col>
              </Row>
            </Col>
          </Row>
        </>
      )}
    </div>
  );
};

export const RECOMMENDED_ERROR_KEYS = {
  error_on_leave: 'error_on_leave',
  invalid_passport: 'invalid_passport',
  error_ntbe: 'error_ntbe',
  invalid_certificate_of_competence: 'invalid_certificate_of_competence',
  incomplete_training_req: 'incomplete_training_req',
  incomplete_investigation_training_req: 'incomplete_investigation_training_req',
};

const Details = ({ ga4react }) => {
  const history = useHistory();
  const { roleConfig, userProfile } = useAccess();
  const location = useLocation();
  const { seafarerId, step = 'general' } = useParams();
  const searchParams = new URLSearchParams(location.search);
  const hkid = searchParams.get('hkid');
  const [error, setError] = useState(null);
  const [seafarer, setSeafarer] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [screeningInfo, setScreeningInfo] = useState(null);
  const [isRefreshFlag, setIsRefreshFlag] = useState(false);
  const [suptAppraisalData, setSuptAppraisalData] = useState([]);

  const [showParentErrorModal, setShowParentErrorModal] = useState(false);
  const [showDuplicateHKIDModal, setShowDuplicateHKIDModal] = useState(false);
  const [hasStatusChanged, setHasStatusChanged] = useState(0);
  const [hasHistoryChanged, setHasHistoryChanged] = useState(0);

  const dropDownOfficeData = [];
  const [activeVesselData, setActiveVesselData] = useState([]);
  const hasRoleAccess =
    roleConfig.seafarer.view.general &&
    checkUserBelongToSameShipParty(dropDownOfficeData, seafarer, roleConfig);
  const [hasAccess, setHasAccess] = useState(hasRoleAccess);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [unableToRecommendErrors, setUnableToRecommendErrors] = useState([]);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [filteredHistoryUpdated, setFilteredHistoryUpdated] = useState(false);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer Details');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    (async () => {
      if (hkid) {
        try {
          const { data } = await seafarerService.getSeafarerByHKID(hkid);
          const seafarerId = data?.results[0]?.id;
          history.push(`/seafarer/details/${seafarerId}/appraisals`);
        } catch {
          setError(`Oops, something went wrong. Please try again. Error: ${error}`);
          if (error.response.status === 403) {
            setHasAccess(false);
          }
        }
      } else {
        try {
          setError(null);
          setIsLoading(true);
          const paramsForFetchingSuptAppraisalList = `?limit=100&seafarer_id=${seafarerId}`;
          let response;
          if (step === 'general') {
            response = await Promise.allSettled([
              seafarerService.getSeafarer(seafarerId, true),
              vesselService.queryVesselOwnership(),
              seafarerSurveyService.getSuptAppraisalList(paramsForFetchingSuptAppraisalList),
            ]);
          } else {
            response = await Promise.allSettled([
              seafarerService.getSeafarer(seafarerId, true),
              vesselService.queryVesselOwnership(),
            ]);
          }
          if (response[0].status === 'fulfilled') {
            setSeafarer(response[0]?.value?.data);
            const person = response[0]?.value?.data?.seafarer_person ?? {};
            const screeningResponse = await getScreeningStatusInfo(
              person.screening_status,

              person.id,
            );
            setScreeningInfo(screeningResponse);
          }
          if (response[1].status === 'fulfilled') {
            setActiveVesselData(response[1].value);
          }
          if (step === 'general') {
            if (response[2].status === 'fulfilled') {
              setSuptAppraisalData(response[2]?.value?.data.results);
            }
          }
          if (response[0].status === 'rejected') {
            throw response[0].reason;
          }
        } catch (error) {
          setError(`Oops, something went wrong. Please try again. Error: ${error}`);
          if (error.response.status === 403) {
            setHasAccess(false);
          }
        } finally {
          setIsLoading(false);
        }
      }
    })();

    if (roleConfig?.seafarer?.edit?.recommendation) {
      const errors = [];
      (async () => {
        try {
          const { BASE_URL } = process.env;
          setIsLoading(true);
          const { data } = await seafarerService.getRecommendedChecks(seafarerId);
          if (data?.error) {
            const messages = data.message.split('||');
            messages.forEach((message) => {
              if (message === RECOMMENDED_ERROR_KEYS.error_on_leave) {
                errors.push(
                  <li>
                    <a
                      href={`${BASE_URL}/seafarer/details/${seafarerId}/status-history`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Please change the <b>Status</b> of the seafarer to <b>On Leave</b>
                    </a>
                  </li>,
                );
              }
              if (message === RECOMMENDED_ERROR_KEYS.invalid_passport) {
                errors.push(
                  <li>
                    <a
                      href={`${BASE_URL}/seafarer/details/${seafarerId}/id-documents`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Please upload a <b>Passport</b> with an expiry date more than <b>1 year</b>
                    </a>
                  </li>,
                );
              }

              if (message === RECOMMENDED_ERROR_KEYS.error_ntbe) {
                errors.push(
                  <li>
                    <a href={`${BASE_URL}/seafarer/archived`} target="_blank" rel="noreferrer">
                      Recommeding this seafarer is not possible because the seafarer is marked as{' '}
                      <b>Not To Be Employed</b>
                    </a>
                  </li>,
                );
              }

              if (message === RECOMMENDED_ERROR_KEYS.invalid_certificate_of_competence) {
                errors.push(
                  <li>
                    <a
                      href={`${BASE_URL}/seafarer/details/${seafarerId}/other-documents`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Please upload a <b>Certificate of Competence (Is National)</b>
{' '}
with an expiry
                      date more than
{' '}
                      <b>1 year</b>
                    </a>
                  </li>,
                );
              }
              if (message === RECOMMENDED_ERROR_KEYS.incomplete_training_req) {
                errors.push(
                  <li>
                    <a
                      href={`${BASE_URL}/seafarer/details/${seafarerId}/appraisals`}
                      target="_blank"
                      rel="noreferrer"
                      data-testid="fml-seafarer-unable-to-recommend-error-incomplete-training-req"
                    >
                      Recommending this seafarer is not possible because the seafarer still has{' '}
                      <b>incomplete training requirements </b>
                    </a>
                  </li>,
                );
              }
              if (message === RECOMMENDED_ERROR_KEYS.incomplete_investigation_training_req) {
                errors.push(
                  <li>
                    <a
                      href={`${BASE_URL}/seafarer/details/${seafarerId}/appraisals`}
                      target="_blank"
                      rel="noreferrer"
                      data-testid="fml-seafarer-unable-to-recommend-error-incomplete-investigation-training-req"
                    >
                      Recommending this seafarer is not possible because the seafarer still has{' '}
                      <b>incomplete investigation of training requirements</b>
                    </a>
                  </li>,
                );
              }
            });
          }
          setIsLoading(false);
        } catch (error) {
          setIsLoading(false);
          setError(`Oops, something went wrong. Please try again. Error: ${error}`);
        }
      })();
      setUnableToRecommendErrors(errors);
    }
  }, [hasStatusChanged, seafarerId, isRefreshFlag]);

  useEffect(() => {
    if (seafarer) {
      (async () => {
        try {
          const response = await seafarerService.getSeafarerStatusHistoryByPersonID(
            seafarer.seafarer_person_id, { latest_crew_assignment: true });

          setFilteredHistory(response?.data ?? []);
          setFilteredHistoryUpdated(true);
        } catch (error) {
          setError(`Oops, something went wrong. Please try again. Error: ${error}`);
        }
      })();
    }
  }, [seafarer, hasStatusChanged, hasHistoryChanged]);

  const getScreeningStatusInfo = async (status, seafarerPersonId) => {
    switch (status) {
      case screeningStatus.UNDER_SCREENING:
        return {
          message: 'This seafarer is under screening.',
          type: 'info',
        };
      case screeningStatus.PASSED:
        return {
          message: 'This seafarer has passed screening.',
          type: 'success',
        };
      case screeningStatus.REJECTED: {
        return {
          message: 'This seafarer screening has been rejected.',
          type: 'danger',
        };
      }
      default:
        return { message: '', type: 'info' };
    }
  };

  const refreshDetailsPageData = () => {
    setIsRefreshFlag(!isRefreshFlag);
  };

  const parentErrorModal = () => {
    return (
      <Modal show={showParentErrorModal} centered>
        <Modal.Header>
          <Modal.Title>
            Unable to mark this Main Profile as other page’s duplicate Secondary Profile
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          If you found a duplicate of this page, go to that page to mark duplicate HKID with this
          page’s HKID.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowParentErrorModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    );
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={hasAccess}>
      <Container>
        <NotificationProvider>
          {showDuplicateHKIDModal && (
            <MarkAsDuplicateModal
              showDuplicateHKIDModal={showDuplicateHKIDModal}
              setShowDuplicateHKIDModal={setShowDuplicateHKIDModal}
              parent_hkid={seafarer?.parent_hkid}
              seafarerId={seafarerId}
              setHasStatusChanged={setHasStatusChanged}
              dropDownOfficeData={dropDownOfficeData}
              roleConfig={roleConfig}
            />
          )}

          {parentErrorModal()}
          {error ? <ErrorAlert message={error} /> : ''}
          {seafarer && !isLoading ? (
            <>
              {screeningInfo && (
                <RenderSeafarer
                  roleConfig={roleConfig}
                  seafarer={seafarer}
                  seafarerId={seafarerId}
                  setError={setError}
                  isLoading={isLoading}
                  setIsLoading={setIsLoading}
                  setShowParentErrorModal={setShowParentErrorModal}
                  setShowDuplicateHKIDModal={setShowDuplicateHKIDModal}
                  history={history}
                  ga4react={ga4react}
                  hasStatusChanged={hasStatusChanged}
                  setHasHistoryChanged={setHasHistoryChanged}
                  setHasStatusChanged={setHasStatusChanged}
                  refreshDetailsPageData={refreshDetailsPageData}
                  activeVesselData={activeVesselData}
                  unableToRecommendErrors={unableToRecommendErrors}
                  screeningInfo={screeningInfo}
                  filteredHistory={filteredHistory}
                  filteredHistoryUpdated={filteredHistoryUpdated}
                  suptAppraisalData={suptAppraisalData}
                  user={userProfile}
              />
              )}
            </>
          ) : (
            <div className="spinner-container">
              <Spinner />
            </div>
          )}
          <ScrollArrow />
        </NotificationProvider>
      </Container>
    </AccessHandlerWrapper>
  );
};

export default Details;
