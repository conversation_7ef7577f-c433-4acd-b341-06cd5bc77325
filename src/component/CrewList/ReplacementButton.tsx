import React, { SyntheticEvent } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'react-bootstrap';

interface Props {
  seafarerId: number;
  eventTracker: Function;
}

const ReplaceButton = ({ seafarerId, eventTracker }: Props) => {
  const history = useHistory();
  const routeToAvailableSeafarer = (event: SyntheticEvent, seafarerId: number) => {
    event.stopPropagation();
    history.push(`/seafarer/available-seafarers/${seafarerId}`);
  };

  return (
    <Button
      onClick={(e) => {
        eventTracker('replaceSeafarer', 'Routes to Available Seafarers');
        routeToAvailableSeafarer(e, seafarerId);
      }}
      variant="outline-primary"
    >
      Replace
    </Button>
  );
};

export default ReplaceButton;
