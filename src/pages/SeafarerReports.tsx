import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import {
  Container,
  Col,
  Row,
  Tab,
  Button,
  Form,
  ButtonGroup,
  Dropdown,
  ButtonToolbar,
} from 'react-bootstrap';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { SeafarerReportData, SEAFARER_REPORT_PAGE } from '../model/TabData';
import TabWrapper from '../component/common/TabWrapper';
import ModellerTable from '../component/SeafarerReport/Modeller/ModellerListTable';
import { getModellerItems } from '../component/SeafarerReport/Modeller/MenuList';
import ScrollArrow from '../component/BackToTopButton';
import ModellerFooter from '../component/SeafarerReport/Modeller/Footer';
import { modellerFooterItems } from '../component/SeafarerReport/Modeller/FooterItem';
import { useDebounce, useDebouncedCallback } from 'use-debounce';
import SearchParametersView from '../component/SearchParametersView';
import AdvancedSearch from '../component/advanced_search/AdvancedSearch';
import AdvancedSearchMobile from '../component/advanced_search/AdvancedSearchMobile';
import {
  mapQueryStringToSearchCriteria,
  generateQuickSearch,
  addSortOrPaginateParams,
  addSortOrPaginateParamsForReports,
  getDateQuery,
} from '../util/advance-search/search-query';
import SearchController from '../controller/search-controller';
import httpService from '../service/http-service';
import _, { isArray } from 'lodash';
import defaultFilters from '../util/advance-search/seafarer-report-default-filters';
import seafarerReportService from '../service/seafarer-report-service';
import {
  Filter,
  ModellerReportResults,
  ModellerReportSummary,
  // eslint-disable-next-line no-unused-vars
  PaginateData,
  approvalStatus,
  reportApiPayloadFilterKeys,
  reportFilterKeys,
  reportTabNames,
  reportTabNamesFilter,
} from '../types/seafarerReports';
import GA4React from 'ga-4-react';
import ApprovalReport from '../component/SeafarerReport/approval-report/approval-report';
import TableColumnsButton from '../component/seafarerList/TableColumnsButton';
import approvalReportColumnDefs from '../component/SeafarerReport/approval-report/approval-report-column-defs';
import {
  dateOfCommencementKey,
  joiningDateQueryKey,
  recommendedStatusKey,
  statusDateQueryKey,
} from '../util/advance-search/search-types';
import SignedOnReports from '../component/SeafarerReport/signed-on/signed-on';
import signedOnReportsColumnDefs from '../component/SeafarerReport/signed-on/signed-on-column-defs';
import signedOffReportsColumnDefs from '../component/SeafarerReport/signed-off/signed-off-column-defs';
import ErrorAlert from '../component/common/ErrorAlert';
import DgShippingList from '../component/SeafarerReport/dg-shipping-list/dg-shipping-list';
import dgShippingListColumnDefs from '../component/SeafarerReport/dg-shipping-list/dg-shipping-list-column-defs';
import ExportToExcelModal from '../component/SeafarerReport/export-to-excel/export-to-excel-modal';
import SuccessAlert from '../component/common/SuccessAlert';
import { seafarerStatus } from '../model/constants';
import SignedOffReports from '../component/SeafarerReport/signed-off/signed-off';
import { useAccess } from '@src/component/common/Access';

/*global Props, roleConfig, ga4react*/
/*eslint no-undef: "error"*/
interface Props {
  ga4react: GA4React;
}

export interface ExportToExcelPayload {
  remarks: string;
  exportedBy: string;
  seafarers?: Array<Object>;
  reportType?: reportTabNames;
  filters?: Object;
}

const DEFAULT_TOP_MARGIN = 150;
const EVERY_ITEM = 60;
const initialPagination = {
  pageSize: 10,
  pageIndex: 0,
  sortBy: [
    {
      id: 'vessel_name',
      desc: false,
    },
  ],
};

const checkExportToExcelAccess = (roleConfig, tabName) => {
  if (roleConfig.seafarer?.edit) {
    if (tabName === reportTabNames.DG_SHIPPING_LIST) {
      return roleConfig.seafarer.edit.seafarerDgShippingExportToExcel;
    }
    else if (tabName === reportTabNames.SIGNED_OFF) {
      return false; // since we don't want to give the export option to anyone
    }
    return roleConfig.seafarer.edit.seafarerExportToExcel;
  }
};

const SeafarerReports = ({ ga4react }: Props) => {
  const initSort = initialPagination.sortBy;
  const { tab }: { tab: string } = useParams();
  const history = useHistory();
  const { roleConfig, userProfile } = useAccess();
  const [activeKey, setActiveKey] = useState(tab);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [summary, setSummary] = useState<ModellerReportSummary>();
  const [loading, setLoading] = useState(false);
  const query = useLocation().search.substring(1);
  const [apiQuery, setApiQuery] = useState('');
  const criteria = mapQueryStringToSearchCriteria(query, null, tab, SEAFARER_REPORT_PAGE);
  const hasNoSearchCriteria = criteria.length === 0;
  const [showAdvSearchMobile, setShowAdvSearchMobile] = useState(false);
  const [showAdvSearch, setShowAdvSearch] = useState(false);
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [filteredRecords, setFilteredRecords] = useState<ModellerReportResults[]>([]);
  const [filteredPageCount, setFilteredPageCount] = useState(0);
  const [filters, setFilters] = useState<Filter[]>([]);
  const [debouncedFilters] = useDebounce(filters, 700);
  const searchInputRef = useRef(null);
  const [dropDownData, setDropDownData] = useState();
  // eslint-disable-next-line no-unused-vars
  const tableRef = useRef(null);

  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [tableColumnItems, setTableColumnItems] = useState([]);
  const [error, setError] = useState('');
  const [selectedRows, setSelectedRows] = useState([]);
  const [showExportToExcelModal, setShowExportToExcelModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [ownershipDetails, setOwnershipDetails] = useState([]);
  const [signedOnSeafarerList, setSignedOnSeafarerList] = useState([]);
  const hasExportToExcelAccess = useRef<boolean>(checkExportToExcelAccess(roleConfig, tab));

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    if (tab === reportTabNames.SIGNED_ON) {
      const vesselDetailsMap = new Map(ownershipDetails.map(i => [i.id, i]));
      const updateFilteredRecords = signedOnSeafarerList.map((record) => {
        const details = vesselDetailsMap.get(record.vesselOwnershipId);
        return {
          ...record,
          owner: details ? details.owner.value : undefined,
        };
      });
      setFilteredRecords(updateFilteredRecords);
    }
  }, [ownershipDetails, signedOnSeafarerList]);

  const onSelectRow = (rows) => {
    setSelectedRows(rows);
  };

  useEffect(() => {
    loadDropDownDataAndDefaultFilters();
    hasExportToExcelAccess.current = checkExportToExcelAccess(roleConfig, tab);
    switch (tab) {
      case reportTabNames.APPROVAL_REPORT: {
        const approvalReportColumns = _.cloneDeep(
          approvalReportColumnDefs({
            selectedRows,
            onSelectRow,
            data: filteredRecords,
            hasExportToExcelAccess: hasExportToExcelAccess.current,
          }).filter((col) => !col.sticky),
        );
        setSelectedColumns(approvalReportColumns);
        setTableColumnItems(approvalReportColumns);
        break;
      }
      case reportTabNames.SIGNED_ON: {
        const signedOnColumns = _.cloneDeep(
          signedOnReportsColumnDefs({
            selectedRows,
            onSelectRow,
            data: filteredRecords,
            hasExportToExcelAccess: hasExportToExcelAccess.current,
          }).filter((col) => !col.sticky),
        );
        setSelectedColumns(signedOnColumns);
        setTableColumnItems(signedOnColumns);
        break;
      }
      case reportTabNames.SIGNED_OFF: {
        const signedOffColumns = _.cloneDeep(
          signedOffReportsColumnDefs({
            selectedRows,
            onSelectRow,
            data: filteredRecords,
            hasExportToExcelAccess: hasExportToExcelAccess.current,
          }).filter((col) => !col.sticky),
        );
        setSelectedColumns(signedOffColumns);
        setTableColumnItems(signedOffColumns);
        break;
      }
      case reportTabNames.DG_SHIPPING_LIST: {
        const dgShippingListColumns = _.cloneDeep(
          dgShippingListColumnDefs({
            selectedRows,
            onSelectRow,
            data: filteredRecords,
            hasExportToExcelAccess: hasExportToExcelAccess.current,
          }).filter((col) => !col.sticky),
        );
        setSelectedColumns(dgShippingListColumns);
        setTableColumnItems(dgShippingListColumns);
        break;
      }
      default:
        break;
    }
  }, [tab]);

  useEffect(() => {
    if (debouncedFilters.length) {
      let filteredList = removeEmptyFilters(debouncedFilters);
      if (filteredList.length === 0) {
        window.history.replaceState({}, '', `${history.location.pathname}`);
        setApiQuery('');
        return;
      }
      const controller = new SearchController();
      const apiFilterQuery = controller.getQuery(filteredList);
      setApiQuery(apiFilterQuery);
      const filterQuery = controller.getQuery(
        filteredList.filter((i) => i.defaultTab === undefined),
      );
      window.history.replaceState({}, '', `${history.location.pathname}?${filterQuery}`);
    } else {
      window.history.replaceState({}, '', `${history.location.pathname}`);
      setApiQuery('');
    }
  }, [debouncedFilters]);

  useEffect(() => {
    setSelectedRows([]);
  }, [filteredRecords]);

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value: string) => {
    switch (type) {
      case 'tabs':
        if (value === reportTabNames.MODELLER) {
          ga4EventTrigger(
            'Modeller Tab',
            'Seafarer Reports - Tab',
            `Seafarer Reports Page - ${value}`,
          );
        } else {
          ga4EventTrigger('Tab', 'Nav', `Seafarer Reports Page - ${value}`);
        }
        break;
      case 'pageSwitch':
        ga4EventTrigger('Page Number', 'Pagination', `Seafarer Reports Page - ${value}`);
        break;
      case 'pageSizeSwitch':
        ga4EventTrigger('Number Of Rows', 'Pagination', `Seafarer Reports Page - ${value}`);
        break;
      case 'columnDisplay':
        ga4EventTrigger('Column', 'List', `Seafarer Reports Page - ${value}`);
        break;
      case 'KeywordSearch':
        ga4EventTrigger('Keyword', 'Keyword Search', `Seafarer Reports Page - ${value}`);
        break;
      case 'KeywordSearchClick':
        ga4EventTrigger('Click', 'Keyword Search', 'Seafarer Reports Page');
        break;
      case 'advancedSearch':
        ga4EventTrigger('Click', 'Advance Search', `Seafarer Reports Page - ${value}`);
        break;
      case 'filterTypeChange':
        ga4EventTrigger('Category', 'Advance Search', `Seafarer Reports Page - ${value}`);
        break;
      case 'filterSubTypeChange':
        ga4EventTrigger('Value', 'Advance Search', `Seafarer Reports Page - ${value}`);
        break;
      case 'sortBy':
        ga4EventTrigger('Sorting', 'List', `Seafarer Reports Page: Sort By - ${value}`);
        break;
      case 'scroll':
        ga4EventTrigger('Back to Top', 'Nav', `Back To Top`);
        break;
      case 'actions':
        ga4EventTrigger('List More', 'Menu', `Seafarer Reports Page - ${value}`);
        break;
      case 'moreDropdown':
        ga4EventTrigger('More', 'Menu', `Seafarer Reports Page - ${value}`);
        break;
      case 'modellerReportVesselPlanLink':
        ga4EventTrigger('View Modeller Report Details', 'Modeller Report - link', value ?? '');
        break;
      case 'modellerReportPencilEditButton':
        ga4EventTrigger('Edit Modeller from List', 'Modeller Report - menu', value ?? '');
        break;
      case 'exportModellerSummary':
        ga4EventTrigger('Export Modeller Summary', 'Modeller Report - menu', value ?? '');
        break;
      default:
        ga4EventTrigger('Click', 'Seafarer Modeller Report', value);
        break;
    }
  };

  const loadDropDownDataAndDefaultFilters = async () => {
    try {
      /*using window location instead of active key because when user quickly switches
          tab during initial load activekey new value is not reflected yet during this function execution*/
      const tab =
        window.location.pathname
          .split('/')
          .find((i) => SeafarerReportData.map((i) => i.eventKey).includes(i)) ?? activeKey;
      setDefaultFilters(tab, mapQueryStringToSearchCriteria(query, {}, tab, SEAFARER_REPORT_PAGE));
      const searchController = new SearchController();
      const result = await searchController.onLoadPage(SEAFARER_REPORT_PAGE);
      setDefaultFilters(
        tab,
        mapQueryStringToSearchCriteria(query, result.dropDownData, tab, SEAFARER_REPORT_PAGE),
      );
      setOwnershipDetails(result.dropDownData.ownership_details);
      setDropDownData(result.dropDownData);
      setShowAdvSearch(true);
    } catch (error) {
      console.log('## error', error);
    }
  };

  const handleTabSelect = (key: string) => {
    if (key === activeKey) {
      return;
    }
    setActiveKey(key);
    setPageCount(0);
    setFilteredRecords([]);
    setFilteredPageCount(0);
    setTotalCount(0);
    eventTracker('tabs', key);
    setIsPageViewInvoked(false);
    setFilters([]);
    history.push(`/${SEAFARER_REPORT_PAGE}/${key}`);
  };

  const handleKeywordChange = useDebouncedCallback((value) => {
    eventTracker('KeywordSearch', value);
    setSearchedKeyword(value.trim().toLowerCase());
  }, 500);

  const removeEmptyFilters = (filter: Filter[]) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  const ontoggle = () => {
    eventTracker('advancedSearch', `${showAdvSearch ? 'close' : 'open'}`);
    setShowAdvSearch(!showAdvSearch);
  };

  const getReportsData = async (queryParams, tabName) => {
    let ownerParam = '';
    let paramsArray = [];
    switch (tabName) {
      case reportTabNames.MODELLER: {
        const modellerReportResponse = await seafarerReportService.getModellerReports(queryParams);
        return modellerReportResponse;
      }
      case reportTabNames.APPROVAL_REPORT: {
        queryParams = [queryParams, 'reportType=APPROVAL_REPORT'].join('&');
        const approvalReportResponse = await seafarerReportService.getSeafarerReports(queryParams);
        return approvalReportResponse;
      }
      case reportTabNames.SIGNED_ON: {
        queryParams = [queryParams, 'reportType=SIGNED_ON_REPORT'].join('&');
        paramsArray = queryParams.split('&');
        ownerParam = paramsArray.find(param => param.startsWith('owner='));
        if (ownerParam) {
          const ownerValue = ownerParam?.split('=')[1];
          const ownerIds = ownerValue?.split('%7C').map((id) => parseInt(id, 10));
          const filteredOwnershipIds = ownershipDetails.reduce((acc, detail) => {
            if (ownerIds.includes(detail?.owner?.ship_party_id)) {
              acc.push(detail.id);
            }
            return acc;
          }, []);
          queryParams = queryParams
            .split('&')
            .filter((param) => !param.startsWith('owner='))
            .concat(
              `owner=${filteredOwnershipIds.length > 0 ? filteredOwnershipIds.join('%7C') : '0'}`,
            )
            .join('&');
          const signedOnReportResponse = await seafarerReportService.getSeafarerReports(queryParams);
          return signedOnReportResponse;
        }
        const signedOnReportResponse = await seafarerReportService.getSeafarerReports(queryParams);
        return signedOnReportResponse;
      }
      case reportTabNames.SIGNED_OFF: {
        queryParams = [queryParams, 'reportType=SIGNED_OFF_REPORT'].join('&');
        paramsArray = queryParams.split('&');
        ownerParam = paramsArray.find(param => param.startsWith('owner='));
        if (ownerParam) {
          const ownerValue = ownerParam?.split('=')[1];
          const ownerIds = ownerValue?.split('%7C').map((id) => parseInt(id, 10));
          const filteredOwnershipIds = ownershipDetails.reduce((acc, detail) => {
            if (ownerIds.includes(detail?.owner?.ship_party_id)) {
              acc.push(detail.id);
            }
            return acc;
          }, []);
          queryParams = queryParams
            .split('&')
            .filter((param) => !param.startsWith('owner='))
            .concat(
              `owner=${filteredOwnershipIds.length > 0 ? filteredOwnershipIds.join('%7C') : '0'}`,
            )
            .join('&');
          const signedOffReportResponse = await seafarerReportService.getSeafarerReports(queryParams);
          return signedOffReportResponse;
        }
        const signedOffReportResponse = await seafarerReportService.getSeafarerReports(queryParams);
        return signedOffReportResponse;
      }

      case reportTabNames.DG_SHIPPING_LIST: {
        queryParams = [queryParams, 'reportType=DG_SHIPPING_REPORT'].join('&');
        const dgShippingReportResponse = await seafarerReportService.getSeafarerReports(
          queryParams,
        );
        return dgShippingReportResponse;
      }
      default:
        break;
    }
  };

  const queryListData = async (sortPaginateData: PaginateData) => {
    try {
      setLoading(true);
      setPageCount(0);
      setFilteredRecords([]);
      setFilteredPageCount(0);
      setTotalCount(0);
      if (
        [
          reportTabNames.APPROVAL_REPORT,
          reportTabNames.SIGNED_ON,
          reportTabNames.SIGNED_OFF,
          reportTabNames.DG_SHIPPING_LIST,
        ].includes(tab)
      ) {
        const { pageSize, pageIndex } = sortPaginateData;
        sortPaginateData.offset = pageIndex * pageSize;
      }

      let queryParams = generateQuickSearch({ keyword: searchedKeyword });
      if (tab === reportTabNames.MODELLER) {
        queryParams = addSortOrPaginateParams(sortPaginateData, queryParams);
      } else {
        queryParams = addSortOrPaginateParamsForReports(sortPaginateData, queryParams);
      }

      if (filters.length) {
        let filteredList = _.cloneDeep(removeEmptyFilters(filters));
        if (filteredList.length) {
          filteredList.forEach((filter) => {
            if ([joiningDateQueryKey, dateOfCommencementKey].includes(filter.type.queryKey)) {
              filter.type.queryKey = statusDateQueryKey;
            }
            if (recommendedStatusKey === filter.type.queryKey) {
              filter.type.queryKey = reportFilterKeys.recommended;
            }
          });
          const controller = new SearchController();
          const filterQuery = controller.getQuery(filteredList, tab);
          queryParams = queryParams + '&' + filterQuery;
        }
      } else {
        queryParams += `&${apiQuery}`;
      }
      const { data: response } = await getReportsData(queryParams, tab);
      let seafarerRecords = response.results;
      const filteredRecords = seafarerRecords.map((item) => {
        return { ...item, keyword: searchedKeyword };
      });
      if (tab === reportTabNames.SIGNED_ON) {
        setSignedOnSeafarerList(filteredRecords);
      } else {
        setFilteredRecords(filteredRecords);
      }
      setTotalCount(response.pagination.totalCount);
      setSummary(response.summary);
      setFilteredPageCount(Math.ceil(response.pagination.totalCount / sortPaginateData.pageSize));
      if (response.pagination) {
        setPageCount(Math.ceil(response.pagination.totalCount / sortPaginateData.pageSize));
      }
      setLoading(false);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      setError(`Error: ${error}`);
      console.log('error:', error);
      setLoading(false);
    }
  };
  const getData = useCallback(
    async ({ pageSize, pageIndex, sortBy }: PaginateData) => {
      // Quick search active
      if (searchedKeyword.length && hasNoSearchCriteria) {
        handleQuickSearch({ pageSize, pageIndex, sortBy });
      } else {
        await fetchData({ pageSize, pageIndex, sortBy });
      }
    },
    [searchedKeyword, activeKey, query, apiQuery, tab],
  );

  const handleQuickSearch = useDebouncedCallback(
    async (sortPaginateData) => {
      await queryListData(sortPaginateData);
    },
    700,
    // @ts-ignore
    [query, searchedKeyword, activeKey, apiQuery],
  );

  const fetchData = useCallback(
    async (sortPaginateData: PaginateData) => {
      await queryListData(sortPaginateData);
    },
    [query, searchedKeyword, activeKey, apiQuery, tab],
  );

  const clearFilters = () => {
    const filteredList = removeEmptyFilters(filters);
    if (filteredList.length === 0) {
      setFilters([]);
    }
    if (searchedKeyword.length || filteredList.length) {
      if (searchInputRef?.current) {
        // @ts-ignore
        searchInputRef.current.value = '';
      }
      setSearchedKeyword('');
      setFilters([]);
      setFilteredRecords([]);
      setFilteredPageCount(0);
      setLoading(false);
      history.push(`/${SEAFARER_REPORT_PAGE}/${activeKey}`);
    }
    eventTracker('clearAll', 'Clear All');
  };

  const setDefaultFilters = (
    key: string,
    additionalQueryFilters: Filter[],
    defaultFilterData = null,
  ) => {
    const controller = new SearchController();
    let additionalFilters = additionalQueryFilters
      ? additionalQueryFilters.filter((i) => {
          return i.type.validTabs === undefined || i.type.validTabs.includes(key);
        })
      : undefined;
    let filterArray = [...filters].filter((i) => {
      return (
        (i.defaultTab === undefined || i.defaultTab === key) &&
        (i.type.validTabs === undefined || i.type.validTabs.includes(key))
      );
    });

    let newFilter: Filter[] = [];
    if (defaultFilterData) {
      newFilter = defaultFilters(key, defaultFilterData);
    } else {
      newFilter = defaultFilters(key);
    }
    if (additionalFilters) {
      newFilter = newFilter.map(
        (i) => (i = additionalFilters?.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = newFilter.map((i) => i?.type?.type);
      additionalFilters = additionalFilters.filter((i) => !existingFilter.includes(i?.type?.type));
      setFilters([...newFilter, ...filterArray, ...additionalFilters]);
      setApiQuery(controller.getQuery([...newFilter, ...filterArray, ...additionalFilters]));
    } else {
      newFilter = newFilter.map(
        (i) => (i = filterArray.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = newFilter.map((i) => i?.type?.type);
      filterArray = filterArray.filter((i) => !existingFilter.includes(i?.type?.type));
      setFilters([...newFilter, ...filterArray]);
      setApiQuery(controller.getQuery([...newFilter, ...filterArray]));
    }
  };

  const displayQuickSearchResult = searchedKeyword.length && hasNoSearchCriteria;

  const currentPageCount = displayQuickSearchResult ? filteredPageCount : pageCount;

  const handleExportToExcel = async () => {
    setShowExportToExcelModal(true);
  };

  const prepareFiltersForExcelPayload = () => {
    const filteredList = _.cloneDeep(removeEmptyFilters(filters)).filter((currentFilter) => {
      if (isArray(currentFilter.subtype) && currentFilter.subtype.length) {
        return currentFilter.subtype[0].id !== 0;
      }
      return true;
    });
    const filtersObj = {};
    const statusDates = [];
    filteredList.forEach((filter) => {
      const { type } = filter.type;
      if (
        [
          reportFilterKeys.reports_nationalities,
          reportFilterKeys.reports_ranks,
          reportFilterKeys.reporting_offices,
          reportFilterKeys.reports_vessel,
        ].includes(type)
      ) {
        filtersObj[reportApiPayloadFilterKeys[type]] = filter.subtype.map(
          (currentSubtype) => currentSubtype.id,
        );
      } else if (type === reportFilterKeys.approval_status) {
        filtersObj[reportApiPayloadFilterKeys[type]] = filter.subtype.map((currentSubtype) => {
          if (currentSubtype.value === approvalStatus.APPROVED) {
            return seafarerStatus.CREW_ASSIGNMENT_APPROVED;
          } else if (currentSubtype.value === approvalStatus.REJECTED) {
            return seafarerStatus.CREW_ASSIGNMENT_REJECTED;
          } else {
            return seafarerStatus.CREW_ASSIGNMENT_PENDING;
          }
        });
      } else if (
        [reportFilterKeys.date_of_joining, reportFilterKeys.commencement_of_contract].includes(type)
      ) {
        const dateQuery = getDateQuery(filter);
        if (dateQuery) {
          statusDates.push(Object.values(dateQuery).join(','));
        }
      } else if (
        [
          reportFilterKeys.actual_number,
          reportFilterKeys.actual_wages,
          reportFilterKeys.planned_number,
          reportFilterKeys.planned_wages,
        ].includes(type)
      ) {
        const min = filter.subtype.min;
        const max = filter.subtype.max;
        if (min && max) {
          filtersObj[reportApiPayloadFilterKeys[type]] = [`${min},${max}`];
        }
      } else if ([reportFilterKeys.tech_group, reportFilterKeys.vessel].includes(type)) {
        filtersObj[reportApiPayloadFilterKeys[type]] = [
          filter.subtype.map((currentSubtype) => currentSubtype.value).join('|'),
        ];
      } else if (
        [
          reportFilterKeys.sign_on,
          reportFilterKeys.sign_off,
          reportFilterKeys.recommend_date,
          reportFilterKeys.approval_date,
        ].includes(type)
      ) {
        const dateQuery = getDateQuery(filter);
        if (dateQuery) {
          filtersObj[reportApiPayloadFilterKeys[type]] = [Object.values(dateQuery).join(',')];
        }
      } else {
        if (!reportApiPayloadFilterKeys[type]) return;
        filtersObj[reportApiPayloadFilterKeys[type]] = filter.subtype.map(
          (currentSubtype) => currentSubtype.value,
        );
      }
    });
    if (statusDates.length) {
      filtersObj['statusDate'] = statusDates;
    }
    return filtersObj;
  };

  const prepareExcelPayload = (payload: ExportToExcelPayload) => {
    payload['seafarers'] = selectedRows.map((seafarer) => seafarer.id);
    payload['reportType'] = reportTabNamesFilter[tab];
    payload['filters'] = prepareFiltersForExcelPayload();
  };

  const handleExportToExcelSubmit = async (
    setExportButtonLoading: (arg0: boolean) => void,
    payload: ExportToExcelPayload,
  ) => {
    try {
      prepareExcelPayload(payload);
      const { data } = await seafarerReportService.seafarerReportsExportToExcel(payload);

      setSuccessMessage(data.message);
      setSelectedRows([]);
      setExportButtonLoading(false);
      setShowExportToExcelModal(false);
    } catch (err) {
      console.log(err);
      setError(err.message);
      setExportButtonLoading(false);
      setShowExportToExcelModal(false);
    }
  };

  const handleExportToExcelClose = () => {
    setShowExportToExcelModal(false);
  };

  const onSelectColumn = (item) => {
    const newSelection = selectedColumns.slice();
    const idx = newSelection.indexOf(item);
    if (idx !== -1) {
      newSelection.splice(idx, 1);
    } else {
      newSelection.push(item);
      newSelection.sort((a, b) => a.order - b.order);
    }
    setSelectedColumns(newSelection);
  };

  const checkUserHasAccess = () => {
    switch (tab) {
      case reportTabNames.MODELLER:
        return roleConfig?.seafarer?.view?.reportModeller;

      case reportTabNames.APPROVAL_REPORT:
        return roleConfig?.seafarer?.view?.reportApproval;

      case reportTabNames.SIGNED_ON:
        return roleConfig?.seafarer?.view?.reportSignedOn;

      case reportTabNames.SIGNED_OFF:
          return (roleConfig?.seafarer?.view?.reportSignedOff || roleConfig?.seafarer?.edit?.seafarerSuperUser);

      case reportTabNames.DG_SHIPPING_LIST:
        return roleConfig?.seafarer?.view?.reportDgShipping;

      default:
        return false;
    }
  };
  const plannedWagesUnit = filters?.find(
    (f) => f.type.type === reportFilterKeys.planned_wages_unit,
  )?.subtype;

  const advSearchStyle = `${DEFAULT_TOP_MARGIN + (filters.length > 0 ? Math.ceil(filters.length / 2) : 1) * EVERY_ITEM
    }px`;

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig?.seafarer?.view?.general}>
      <Container>
        {showAdvSearchMobile ? (
          <div className="mobileAdvSearch">
            <AdvancedSearchMobile
              setShowAdvSearchMobile={setShowAdvSearchMobile}
              roleConfig={roleConfig}
              filters={filters}
              setFilters={setFilters}
              tab={activeKey}
              page={SEAFARER_REPORT_PAGE}
            />
          </div>
        ) : (
          <>
            {error ? <ErrorAlert message={error} onClose={() => setError('')} /> : ''}
            {successMessage ? (
              <SuccessAlert message={successMessage} onClose={() => setSuccessMessage('')} />
            ) : (
              ''
            )}
            {showExportToExcelModal && (
              <ExportToExcelModal
                email={userProfile.email}
                filteredCount={totalCount}
                handleSubmit={handleExportToExcelSubmit}
                onClose={handleExportToExcelClose}
                selectedRowsCount={selectedRows.length}
              />
            )}
            <h2 className="seafarer-report-heading">Seafarer Reports</h2>
            <Tab.Container activeKey={activeKey} defaultActiveKey="all">
              <Row className="no-print">
                <Col>
                  <TabWrapper handleTabSelect={handleTabSelect} data={SeafarerReportData} />
                </Col>
              </Row>
              <AccessHandlerWrapper hasRoleAccess={checkUserHasAccess()}>
                {
                  <>
                    <Row className="no-print">
                      <Col xs={6} md={3}>
                        <div className="mt-4 quick-filters-button">
                          <Form.Control
                            id="search-bar"
                            type="text"
                            name="keyword"
                            placeholder="Type keywords to filter"
                            ref={searchInputRef}
                            defaultValue=""
                            onClick={() =>
                              eventTracker('KeywordSearchClick', 'Keywork Search Click')
                            }
                            onChange={(e) => handleKeywordChange(e.target.value)}
                          />
                        </div>
                      </Col>
                      <Col xs={6} md="auto">
                        <div className="mobileAdvSearch">
                          <ButtonGroup className="advanced-search-seafarer mt-4 w-100">
                            <Button
                              variant="outline-primary"
                              onClick={() => {
                                setShowAdvSearchMobile(true);
                              }}
                            >
                              Advanced Search
                            </Button>
                          </ButtonGroup>
                        </div>
                        <div className="desktopAdvSearch">
                          <ButtonGroup className="advanced-search-seafarer mt-4">
                            <Dropdown alignRight={false} onToggle={ontoggle} show={showAdvSearch}>
                              <Dropdown.Toggle
                                onClick={ontoggle}
                                variant="outline-primary"
                                id="dropdown-advanced-search"
                              >
                                Advanced Search{' '}
                                {removeEmptyFilters(debouncedFilters).length
                                  ? `(${removeEmptyFilters(debouncedFilters).length})`
                                  : ''}
                              </Dropdown.Toggle>
                              <Dropdown.Menu>
                                <div
                                  className="advanced-search-seafarer-menu"
                                  data-testid="advanced-search-menu"
                                >
                                  <AdvancedSearch
                                    roleConfig={roleConfig}
                                    filters={filters}
                                    setFilters={setFilters}
                                    eventTracker={eventTracker}
                                    tab={activeKey}
                                    title={'Filter Reports'}
                                    page={SEAFARER_REPORT_PAGE}
                                    dropDownDataProp={dropDownData}
                                  />
                                </div>
                              </Dropdown.Menu>
                            </Dropdown>
                          </ButtonGroup>
                        </div>
                      </Col>
                      <Col xs="auto">
                        <Button variant="outline-primary" className="mt-4" onClick={clearFilters}>
                          Clear All
                        </Button>
                      </Col>
                      <Col>
                        <ButtonToolbar className="no-print btn-toolbar justify-content-end mt-4">
                          <ButtonGroup className="mr-2">
                            {activeKey !== reportTabNames.MODELLER && (
                              <TableColumnsButton
                                items={tableColumnItems}
                                selectedColumns={selectedColumns}
                                onSelectColumn={onSelectColumn}
                              />
                            )}
                          </ButtonGroup>
                          <ButtonGroup className="mr-2">
                            {hasExportToExcelAccess.current && (
                              <Button
                                variant="outline-primary"
                                onClick={handleExportToExcel}
                                disabled={!filteredRecords.length}
                              >
                                Export to Excel
                              </Button>
                            )}
                          </ButtonGroup>
                        </ButtonToolbar>
                      </Col>
                    </Row>
                    {filters.length > 0 && (
                      <Row className="mobileAdvSearch">
                        <SearchParametersView
                          criteria={filters}
                          setShowAdvSearchMobile={setShowAdvSearchMobile}
                        />
                      </Row>
                    )}
                    <Row
                      className="desktopAdvSearch"
                      style={{
                        marginTop: showAdvSearch ? advSearchStyle : '0px',
                      }}
                    ></Row>
                  </>
                }
                <Row>
                  <Col>
                    {activeKey === reportTabNames.MODELLER && (
                      <ModellerTable
                        selectedColumns={getModellerItems({
                          selectedRows,
                          onSelectRow,
                          data: filteredRecords,
                          hasExportToExcelAccess: hasExportToExcelAccess.current,
                          plannedWagesUnit,
                        })}
                        fetchData={getData}
                        quickSearchParams={searchedKeyword}
                        advancedSearchParams={apiQuery}
                        data={filteredRecords}
                        loading={loading}
                        dataTestId="modeller-table"
                        init_sort={initSort}
                        tabName={tab}
                        tableRef={tableRef}
                        modellerTotalCount={totalCount}
                        pageCount={currentPageCount}
                        eventTracker={eventTracker}
                        roleConfig={roleConfig}
                      />
                    )}
                    {activeKey === reportTabNames.APPROVAL_REPORT && (
                      <ApprovalReport
                        fetchData={getData}
                        quickSearchParams={searchedKeyword}
                        advancedSearchParams={apiQuery}
                        data={filteredRecords}
                        loading={loading}
                        totalCount={totalCount}
                        selectedColumns={selectedColumns}
                        selectedRows={selectedRows}
                        onSelectRow={(rows) => setSelectedRows(rows)}
                        hasExportToExcelAccess={hasExportToExcelAccess.current}
                      />
                    )}
                    {activeKey === reportTabNames.SIGNED_ON && (
                      <SignedOnReports
                        fetchData={getData}
                        quickSearchParams={searchedKeyword}
                        advancedSearchParams={apiQuery}
                        data={filteredRecords}
                        loading={loading}
                        totalCount={totalCount}
                        selectedColumns={selectedColumns}
                        selectedRows={selectedRows}
                        onSelectRow={(rows) => setSelectedRows(rows)}
                        hasExportToExcelAccess={hasExportToExcelAccess.current}
                      />
                    )}
                    {activeKey === reportTabNames.SIGNED_OFF && (
                      <SignedOffReports
                        fetchData={getData}
                        quickSearchParams={searchedKeyword}
                        advancedSearchParams={apiQuery}
                        data={filteredRecords}
                        loading={loading}
                        totalCount={totalCount}
                        selectedColumns={selectedColumns}
                        selectedRows={selectedRows}
                        onSelectRow={(rows) => setSelectedRows(rows)}
                        hasExportToExcelAccess={hasExportToExcelAccess.current}
                      />
                    )}
                    {activeKey === reportTabNames.DG_SHIPPING_LIST && (
                      <DgShippingList
                        fetchData={getData}
                        quickSearchParams={searchedKeyword}
                        advancedSearchParams={apiQuery}
                        data={filteredRecords}
                        loading={loading}
                        totalCount={totalCount}
                        selectedColumns={selectedColumns}
                        selectedRows={selectedRows}
                        onSelectRow={(rows) => setSelectedRows(rows)}
                        hasExportToExcelAccess={hasExportToExcelAccess.current}
                      />
                    )}
                  </Col>
                </Row>
                {activeKey === reportTabNames.MODELLER && !loading && (
                  <Row>
                    <ModellerFooter
                      columns={modellerFooterItems(summary, plannedWagesUnit)}
                      tableRef={tableRef}
                    />
                  </Row>
                )}
              </AccessHandlerWrapper>
            </Tab.Container>
            <ScrollArrow eventTracker={eventTracker} />
          </>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};
export default SeafarerReports;
