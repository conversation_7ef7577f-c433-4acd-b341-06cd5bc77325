// eslint-disable-next-line no-unused-vars
import moment from 'moment';
import { Filter, FilterType, reportFilterKeys, reportTabNames } from '../../types/seafarerReports';
import SEARCH_TYPES from './search-types';
import { AccountingCurrency, DEFAULT_CURRENCY_MODELLER } from '../../controller/search-controller';

const currentDate = moment().toDate();
const threeMonthsPreviousDate = moment().subtract(3, 'months').toDate();
const fifteenDaysPreviousDate = moment().subtract(15, 'days').toDate();
const fifteenDaysFutureDate = moment().add(15, 'days').toDate();

const defaultFilters = (key: string, response = null) => {
  let filters: Filter[] = [];
  if (key === reportTabNames.MODELLER) {
    filters = [
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === 'planned_wages_unit',
        )[0],
        subtype: AccountingCurrency.find((u) => u.id === DEFAULT_CURRENCY_MODELLER)?.id,
        defaultTab: reportTabNames.MODELLER,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === 'vessel',
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Vessels',
          },
        ],
        defaultTab: reportTabNames.MODELLER,
      },
    ];
  } else if (key === reportTabNames.APPROVAL_REPORT) {
    filters = [
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.recommend_date,
        )[0],
        subtype: {
          startDate: threeMonthsPreviousDate,
          endDate: currentDate,
        },

        defaultTab: reportTabNames.APPROVAL_REPORT,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.reports_vessel,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Vessels',
          },
        ],
        defaultTab: reportTabNames.APPROVAL_REPORT,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.approval_date,
        )[0],
        subtype: {
          startDate: threeMonthsPreviousDate,
          endDate: currentDate,
        },

        defaultTab: reportTabNames.APPROVAL_REPORT,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.reports_ranks,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Ranks',
          },
        ],
        defaultTab: reportTabNames.APPROVAL_REPORT,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.recommended,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All',
          },
        ],
        defaultTab: reportTabNames.APPROVAL_REPORT,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.approval_status,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Status',
          },
        ],
        defaultTab: reportTabNames.APPROVAL_REPORT,
      },
    ];
  } else if (key === reportTabNames.SIGNED_ON || key === reportTabNames.SIGNED_OFF) {
    const  defaultTab = key === reportTabNames.SIGNED_ON ? reportTabNames.SIGNED_ON : reportTabNames.SIGNED_OFF;
    const dateFilterKey = key === reportTabNames.SIGNED_ON ? reportFilterKeys.date_of_joining : reportFilterKeys.sign_off_date;

    filters = [
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === dateFilterKey,
        )[0],
        subtype: {
          startDate: threeMonthsPreviousDate,
          endDate: currentDate,
        },

        defaultTab,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.reports_nationalities,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All',
          },
        ],
        defaultTab,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.reporting_offices,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Reporting Offices',
          },
        ],
        defaultTab,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.report_owners,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Owners',
          },
        ],
        defaultTab,
      },
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.vessel_with_id,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Vessels',
          },
        ],
        defaultTab,
      },
    ];

    if (key === reportTabNames.SIGNED_OFF) {
      filters.push({
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.reports_ranks,
        )[0],
        subtype: [
          {
            id: 0,
            value: 'All Ranks',
          },
        ],
        defaultTab: reportTabNames.SIGNED_OFF,
      });
    }
  } else if (key === reportTabNames.DG_SHIPPING_LIST) {
    filters = [
      {
        type: [...SEARCH_TYPES('seafarer-reports')].filter(
          (i: FilterType) => i.type === reportFilterKeys.commencement_of_contract,
        )[0],
        subtype: {
          startDate: fifteenDaysPreviousDate,
          endDate: fifteenDaysFutureDate,
        },

        defaultTab: reportTabNames.DG_SHIPPING_LIST,
      },
    ];
  }

  return filters;
};

export default defaultFilters;
