import {
  getCrewListPageColumns,
  getSeafarerStatus,
  replaceEmptyWithDashes,
  returnYesNo,
  returnYesNoFromString,
  getAdministrationColor,
  convertToYearsAndMonths,
  getCrewListColumns,
} from '../../component/CrewList/MenuList';
import { seafarerStatus } from '../../model/constants';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');

describe('getCrewListPageColumns', () => {
  test('should return the default columns when no parameters are passed', () => {
    const columns = getCrewListPageColumns();
    expect(columns).toBeInstanceOf(Array);
    expect(columns.length).toBeGreaterThan(0);
    expect(columns.some((col) => col.id === 'hkid')).toBeTruthy();
  });

  test('should include OCIMF-specific columns when isOcimfMatrix is true', () => {
    const columns = getCrewListPageColumns(true);
    expect(columns.some((col) => col.id === 'competancy')).toBeTruthy();
    expect(columns.some((col) => col.id === 'endorsements')).toBeTruthy();
    expect(columns.some((col) => col.id === 'Administration Acceptance')).toBeTruthy();
  });

  test('should not include OCIMF-specific columns when isOcimfMatrix is false', () => {
    const columns = getCrewListPageColumns(false);
    expect(columns.some((col) => col.id === 'competancy')).toBeFalsy();
    expect(columns.some((col) => col.id === 'endorsements')).toBeFalsy();
    expect(columns.some((col) => col.id === 'Administration Acceptance')).toBeFalsy();
  });

  test('should show "Sign On Date" and "Length Of Contract" when activeKey is SIGNED_ON', () => {
    const columns = getCrewListPageColumns(false, seafarerStatus.SIGNED_ON);
    expect(
      columns.find((col) => col.id === 'seafarer_person:seafarer_status_history.status_date')
        .showByDefault,
    ).toBeTruthy();
    expect(
      columns.find((col) => col.id === 'seafarer_length_of_contract').showByDefault,
    ).toBeTruthy();
  });

  test('should not show "Sign On Date" and "Length Of Contract" when activeKey is not SIGNED_ON', () => {
    const columns = getCrewListPageColumns(false, 'someOtherStatus');
    expect(
      columns.find((col) => col.id === 'seafarer_person:seafarer_status_history.status_date')
        .showByDefault,
    ).toBeFalsy();
    expect(
      columns.find((col) => col.id === 'seafarer_length_of_contract').showByDefault,
    ).toBeFalsy();
  });
});

describe('Utility Functions', () => {
  describe('getSeafarerStatus', () => {
    it('should return the correct status name if it exists', () => {
      const jsonData = { active: { name: 'Active' } };
      expect(getSeafarerStatus(jsonData, 'active')).toBe('Active');
    });

    it('should return dashes if status is missing or undefined', () => {
      const jsonData = { active: { name: 'Active' } };
      expect(getSeafarerStatus(jsonData, 'inactive')).toBe('---');
      expect(getSeafarerStatus(jsonData, undefined)).toBe('---');
    });
  });

  describe('replaceEmptyWithDashes', () => {
    it('should return the provided value if not empty', () => {
      expect(replaceEmptyWithDashes('Hello')).toBe('Hello');
    });

    it('should return dashes if value is empty', () => {
      expect(replaceEmptyWithDashes(null)).toBe('---');
      expect(replaceEmptyWithDashes(undefined)).toBe('---');
      expect(replaceEmptyWithDashes('')).toBe('---');
    });
  });

  describe('returnYesNo', () => {
    it('should return "Yes" for truthy values', () => {
      expect(returnYesNo(true)).toBe('Yes');
      expect(returnYesNo(1)).toBe('Yes');
    });

    it('should return "No" for falsy values', () => {
      expect(returnYesNo(false)).toBe('No');
      expect(returnYesNo(0)).toBe('No');
    });

    it('should return dashes for null', () => {
      expect(returnYesNo(null)).toBe('---');
    });
  });

  describe('returnYesNoFromString', () => {
    it('should return "Yes" for string "yes" (case insensitive)', () => {
      expect(returnYesNoFromString('yes')).toBe('Yes');
      expect(returnYesNoFromString('YES')).toBe('Yes');
    });

    it('should return "No" for non-"yes" values', () => {
      expect(returnYesNoFromString('no')).toBe('No');
      expect(returnYesNoFromString('maybe')).toBe('No');
    });

    it('should return dashes for null', () => {
      expect(returnYesNoFromString(null)).toBe('---');
    });
  });

  describe('getAdministrationColor', () => {
    it('should return the correct color mapping', () => {
      expect(getAdministrationColor('Yes')).toEqual({ color: '#218838', background: '#E8F5EB' });
      expect(getAdministrationColor('No')).toEqual({ color: '#C82333', background: '#FAF2F5' });
      expect(getAdministrationColor('N/A')).toEqual({ color: '#6C757D', background: '#EFEFEF' });
      expect(getAdministrationColor('Applied for')).toEqual({
        color: '#F08100',
        background: '#FFF9E8',
      });
    });
  });

  describe('convertToYearsAndMonths', () => {
    it('should convert decimal years correctly', () => {
      expect(convertToYearsAndMonths(1.5)).toBe('1yrs 6m');
      expect(convertToYearsAndMonths(2.75)).toBe('2yrs 9m');
    });

    it('should handle whole numbers', () => {
      expect(convertToYearsAndMonths(3)).toBe('3yrs 0m');
    });

    it('should round months correctly', () => {
      expect(convertToYearsAndMonths(4.999)).toBe('5yrs 0m');
    });
  });
});

describe('getCrewListColumns', () => {
  let columns;

  beforeEach(() => {
    columns = getCrewListColumns();
  });

  test('should return an array', () => {
    expect(Array.isArray(columns)).toBe(true);
  });

  test('should have expected column count', () => {
    expect(columns.length).toBeGreaterThan(0);
  });

  test('should have correct structure for each column', () => {
    columns.forEach((col) => {
      expect(col).toHaveProperty('Header');
      expect(col).toHaveProperty('id');
      expect(col).toHaveProperty('accessor');
      expect(col).toHaveProperty('order');
      expect(col).toHaveProperty('disableSortBy');
      expect(col).toHaveProperty('minWidth');
    });
  });

  test('should have correct id and Header names', () => {
    const columnHeaders = columns.map((col) => col.Header);
    expect(columnHeaders).toEqual(
      expect.arrayContaining([
        'HKID',
        'Name',
        'Rank',
        'End of Contract',
        'Replacement',
        'Account Status',
        'Journey Status',
        'Tech Group',
        'Reporting Office',
        'Nationality',
        'Passport No. ',
        "Seaman's Book",
        'Date of Joining',
        'Repatriation Port',
        'Embarkation Port',
        'Years with FML',
        'Years in Rank',
        'Years on this Vessel Type',
        'Years on all Vessel Type',
        'Months on this vessel tour',
        'Availability Date',
        'Availability Remark',
        'Nearest Airport',
        'Examination',
        'Framo Experience',
        'Cargo Handling Experience',
        'Additional Experience',
        'NTBE',
      ]),
    );
  });

  test('should have expected data transformation functions', () => {
    const hkidColumn = columns.find((col) => col.id === 'hkid');
    expect(typeof hkidColumn.accessor).toBe('function');

    const nameColumn = columns.find((col) => col.id === 'seafarer_person.first_name');
    expect(typeof nameColumn.accessor).toBe('function');
  });

  test('should correctly apply minWidth constraints', () => {
    columns.forEach((col) => {
      expect(col.minWidth).toBeGreaterThan(0);
    });
  });

  test('should have `disableSortBy` set correctly', () => {
    columns.forEach((col) => {
      expect(typeof col.disableSortBy).toBe('boolean');
    });
  });
});
