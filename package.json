{"name": "paris2-seafarer", "repository": "*****************:fleetshipteam/paris2-web-seafarer.git", "author": "", "license": "ISC", "version": "0.1.0", "description": "", "main": "dist/paris2-seafarer.js", "scripts": {"lint": "eslint src", "start": "node check-node-version.js && webpack-dev-server --mode=development --port 9020 --https --hot", "test": "jest", "build": "webpack --mode=production", "deploy": "./jenkins-deploy.sh", "analyze": "webpack --mode=production --env.analyze=true", "prettier": "prettier --write './**'", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install", "storybook": "storybook dev -p 6006 --debug-webpack", "build-storybook": "storybook build"}, "dependencies": {"@types/history": "^4.7.11", "@types/qs": "^6.9.15", "axios": "^0.19.2", "blueimp-md5": "^2.19.0", "crypto-js": "^4.0.0", "currency-symbol-map": "^5.1.0", "downloadjs": "^1.4.7", "enhanced-formik-persist": "^1.0.10", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-sonarjs": "^0.23.0", "formik": "^2.1.6", "formik-persist": "^1.1.0", "ga-4-react": "^0.1.281", "history": "^4.10.1", "html2canvas": "^1.4.1", "i18next": "^19.3.4", "i18next-browser-languagedetector": "^4.0.2", "immutability-helper": "^3.1.1", "jquery": "^1.9.1", "jsonq": "^1.2.0", "jspdf": "^2.5.1", "keycloak-js": "^10.0.1", "libphonenumber-js": "^1.11.13", "lodash": "^4.17.15", "luxon": "^2.1.1", "moment-timezone": "^0.5.31", "paris2-seafarer-status": "^3.0.0", "pdfjs-dist": "2.12.313", "react-18-image-lightbox": "^5.1.4", "react-anchor-link-smooth-scroll": "^1.0.12", "react-bootstrap-icons": "^1.1.0", "react-bootstrap-typeahead": "^6.3.2", "react-datepicker": "^4.25.0", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dnd-touch-backend": "^11.1.3", "react-dropzone": "^11.0.1", "react-i18next": "^11.8.10", "react-infinite-scroll-component": "^6.1.0", "react-pdf": "^5.7.1", "react-phone-input-2": "^2.13.8", "react-router": "^5.1.2", "react-router-bootstrap": "^0.25.0", "react-router-dom": "^5.2.0", "react-table": "^7.2.1", "react-table-sticky": "^1.1.2", "react-toastify": "^10.0.6", "single-spa-react": "^6.0.1", "ts-node": "^10.9.2", "use-debounce": "^6.0.1", "uuid": "^8.3.0", "xlsx": "^0.16.1", "yup": "^0.29.0"}, "peerDependencies": {"prop-types": "15.x", "react": "18.2", "react-bootstrap": "1.x", "react-dom": "18.2"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.9.1", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.9.2", "@chromatic-com/storybook": "^1.6.1", "@storybook/addon-essentials": "^8.1.11", "@storybook/addon-interactions": "^8.1.11", "@storybook/addon-links": "^8.1.11", "@storybook/addon-onboarding": "^8.1.11", "@storybook/addon-webpack5-compiler-swc": "^1.0.4", "@storybook/blocks": "^8.1.11", "@storybook/react": "^8.1.11", "@storybook/react-webpack5": "^8.1.11", "@storybook/test": "^8.1.11", "@tanstack/react-virtual": "^3.8.3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "@types/node": "^22.13.0", "@types/react": "18.2.47", "@types/react-datepicker": "^4.19.5", "@types/react-dom": "18.2.18", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.20", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/eslint-plugin-tslint": "^5.20.0", "@typescript-eslint/parser": "^6.18.1", "autoprefixer": "9.7.4", "babel-core": "6.26.3", "babel-eslint": "^11.0.0-beta.2", "babel-jest": "^29.7.0", "babel-loader": "^8.1.0", "babel-plugin-styled-components": "^2.1.4", "bootstrap": "^4.5.0", "clean-webpack-plugin": "^4.0.0", "concurrently": "^5.1.0", "core-js": "^3.38.0", "css-loader": "^3.5.3", "dotenv": "^8.6.0", "eslint": "^8.56.0", "eslint-config-prettier": "^6.10.1", "eslint-config-react-important-stuff": "^2.0.0", "eslint-plugin-jest": "^23.18.0", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.20.0", "eslint-plugin-storybook": "^0.8.0", "file-loader": "^6.0.0", "html-react-parser": "^1.4.1", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^25.1.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "kremling-loader": "^1.0.2", "postcss-loader": "3.0.0", "prettier": "^2.0.1", "pretty-quick": "^2.0.1", "ramda": "^0.27.0", "react": "18.2", "react-bootstrap": "^1.0.0", "react-dom": "18.2", "sass": "^1.86.0", "sass-loader": "^16.0.5", "storybook": "^8.1.11", "style-loader": "^1.2.1", "styled-components": "^5.1.0", "svg-url-loader": "^6.0.0", "systemjs-webpack-interop": "^2.0.0", "tsconfig-paths-webpack-plugin": "^3.5.2", "typescript": "^4.9.5", "typescript-eslint-parser": "^22.0.0", "webpack": "^5.79.0", "webpack-cli": "^5.0.1", "webpack-config-single-spa-react": "^4.0.4", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "engines": {"node": ">=22"}, "engineStrict": true}