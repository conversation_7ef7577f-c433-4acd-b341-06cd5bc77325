/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { Button } from 'react-bootstrap';

import styleGuide from '../../styleGuide';

import parentHKIDLink from '../common/HKIDLink';
import VesselNameLink from '../CrewList/VesselNameLink';

import Table from '../InfiniteScrollTable';

const { Icon } = styleGuide;

const generateColumns = (selectedColumns, roleConfig, visitSeafarerContactUpdate) => {
  return [
    {
      Header: 'No.',
      id: 'id',
      type: 'item',
      accessor: (row) => {
        return row.id ?? '---';
      },
      sortType: (a, b) => {
        if (a && b) {
          const aName = a.id ? a.id.toLowerCase() : '';
          const bName = b.id ? b.id.toLowerCase() : '';
          if (aName < bName) return -1;
          if (aName > bName) return 1;
        }
        return 0;
      },
      maxWidth: 90,
      sticky: window.innerWidth > 960 ? 'left' : null,
    },
    ...selectedColumns,
    {
      Header: 'Edit Availability',
      id: 'edit-availbility',
      accessor: (row, index) =>
        roleConfig.seafarer.editSeafarer ? (
          <div>
            <Button
              variant="link"
              className="action-column action-button-edit-color"
              onClick={(e) => {
                visitSeafarerContactUpdate(e, row?.id);
              }}
              data-testid={`edit-${index}-availability-btn`}
            >
              <Icon icon="pencil" size={20} className="mr-3" style={{ cursor: 'pointer' }} />
            </Button>
          </div>
        ) : null,
      disableSortBy: true,
      maxWidth: 50,
      sticky: window.innerWidth > 960 ? 'right' : null,
    },
  ];
};

const AvailableSeafarerTable = React.memo(
  ({
    tabName,
    seafarers,
    fetchData,
    visitSeafarer,
    eventTracker,
    selectedColumns,
    loading,
    roleConfig,
    visitSeafarerContactUpdate,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    hasMoreData,
  }) => {
    selectedColumns.forEach((e) => {
      if (e.id === 'hkid') {
        e.accessor = (row) => parentHKIDLink(row, eventTracker);
      } else if (e.id === 'seafarer_experience.vessel_name') {
        e.accessor = (row) => {
          return (
            <VesselNameLink
              ownershipId={row?.latest_experience?.vessel_ownership_id}
              vesselName={row?.latest_experience?.vessel_name}
              eventTracker={eventTracker}
            />
          );
        };
      } else {
        return e;
      }
    });

    let columns = generateColumns(selectedColumns, roleConfig, visitSeafarerContactUpdate);

    if (loading) {
      const removeOnLoad = ['No.'];
      columns = columns.filter((col_obj) => {
        if (col_obj.Header && removeOnLoad.includes(col_obj.Header)) return false;
        return true;
      });
    }
    return (
      <div className="seafarer-table">
        <Table
          tabName={tabName}
          columns={columns}
          data={seafarers}
          fetchData={fetchData}
          eventTracker={eventTracker}
          visitSeafarer={visitSeafarer}
          loading={loading}
          quickSearchParams={quickSearchParams}
          advancedSearchParams={advancedSearchParams}
          init_sort={init_sort}
          hasMoreData={hasMoreData}
        />
      </div>
    );
  },
);

export default AvailableSeafarerTable;
