import qs from 'qs';
import seafarerService from '../service/seafarer-service';
import vesselService from '../service/vessel-service';
import * as searchQuery from '../util/advance-search/search-query';
import { seafarerStatusService } from 'paris2-seafarer-status';
import { SEAFARER_PAGE, SEAFARER_REPORT_PAGE } from '../model/TabData';
import { approvalStatus, recommendationStatus, reportFilterKeys } from '../types/seafarerReports';
import { screeningFilterKeys } from '../model/constants';
import { vesselStatuses } from '@src/constants/status';
import { qsDefaultOptions } from '@src/util';

const accountStatusJson = seafarerStatusService.getAccountStatus();
export const journeyStatusJson = seafarerStatusService.getJourneyStatus();
export const examStatusJson = seafarerStatusService.getExamStatus();

const gender = [
  { id: 1, value: 'male' },
  { id: 2, value: 'female' },
];

const bool = [
  { id: 1, value: 'yes' },
  { id: 2, value: 'no' },
];

const docs_in_hand = [
  { id: 1, value: 'true' },
  { id: 2, value: 'false' },
];

const vessel_size = [
  { id: 0, value: 'All' },
  { id: 1, value: '0 - 20,000', min: 0, max: 20000 },
  { id: 2, value: '20,001 - 50,000', min: 20001, max: 50000 },
  { id: 3, value: '50,001 - 100,000', min: 50001, max: 100000 },
  { id: 4, value: '100,001 - 300,000', min: 100001, max: 300000 },
  { id: 5, value: '300,001 or above', min: 300001, max: '' },
];

const with_fml_vessel_experience = [
  { id: 0, value: 'All' },
  { id: 1, value: 'Yes' },
  { id: 2, value: 'No' },
];

const dataQuality = [
  {
    label: 'Urgent Correction',
    color: 'red',
    name: 'mandatory_data_missing',
  },
  {
    label: 'No Urgent Correction',
    color: 'orange',
    name: 'data_invalid',
  },
  {
    label: 'Data is OK',
    color: 'green',
    name: 'clean',
  },
];

const yearsOfLongServiceType = [
  {
    id: 1,
    value: 'No Gaps',
  },
  {
    id: 2,
    value: 'With Gaps',
  },
];

export const screenForColumnValues = Object.freeze([
  {
    id: 'vessel_recommendation',
    value: 'Vessel Recommendation',
  },
  {
    id: 'new_applicant',
    value: 'New Applicant',
  },
  {
    id: 'data_edit',
    value: 'Data Edit',
  },
]);

const pendingApprovalGroupColumnValues = Object.freeze([
  {
    id: 1,
    value: 'Compliance Employee',
  },
  {
    id: 2,
    value: 'Compliance Supervisor',
  },
  {
    id: 3,
    value: 'Fleet Personnel',
  },
]);

const month_date = [
  {
    id: '01',
    value: 'Jan',
    days: 31,
  },
  {
    id: '02',
    value: 'Feb',
    days: 29,
  },
  {
    id: '03',
    value: 'Mar',
    days: 31,
  },
  {
    id: '04',
    value: 'Apr',
    days: 30,
  },
  {
    id: '05',
    value: 'May',
    days: 31,
  },
  {
    id: '06',
    value: 'Jun',
    days: 30,
  },
  {
    id: '07',
    value: 'Jul',
    days: 31,
  },
  {
    id: '08',
    value: 'Aug',
    days: 31,
  },
  {
    id: '09',
    value: 'Sep',
    days: 30,
  },
  {
    id: '10',
    value: 'Oct',
    days: 31,
  },
  {
    id: '11',
    value: 'Nov',
    days: 30,
  },
  {
    id: '12',
    value: 'Dec',
    days: 31,
  },
];

export const AccountingCurrency = [
  {
    id: 'usd',
    value: 'USD',
  },
  {
    id: 'inr',
    value: 'INR',
  },
];
export const DEFAULT_CURRENCY_MODELLER = 'usd';

const generateOwnerListWithOwnershipIds = (owners, vesselOwnershipData) => {
  const activeVesselOwnershipData = vesselOwnershipData.filter(
    (v) => vesselStatuses.ACTIVE === v.status,
  );
  return owners.map((owner) => {
    return {
      ...owner,
      ownership_ids: activeVesselOwnershipData
        .filter((v) => v.owner?.id === owner.id)
        .map((v) => v.id),
    };
  });
};

const loadSeafarerPageDropDownData = async () => {
  const result = await Promise.allSettled([
    seafarerService.getSeafarerDropDownData(),
    seafarerService.getSeafarerReportingOfficeDropDownData(),
    seafarerService.getTechGroupDropDown(),
    seafarerService.getDropDownDataFromVessel('owners  { id, value }'),
    vesselService.queryVesselOwnership(
      qs.stringify(
        {
          order: 'created_at desc',
          f: [
            'name',
            'id',
            'vessel.id',
            'owner.value',
            'owner.id',
            'vessel.status',
          ],
          status: 'active',
          flatten: true,
        },
        qsDefaultOptions,
      ),
    ),
  ]);
  let dropDownsData = {
    offices: [],
    tech_group: [],
    engine_type: [],
    vessel_type: [],
    planned_vessel: [],
  };
  if (result[0].status === 'fulfilled') {
    dropDownsData = { ...dropDownsData, ...result[0]?.value };
  }
  if (result[1].status === 'fulfilled') {
    dropDownsData['offices'] = result[1]?.value;
  }
  if (result[2].status === 'fulfilled') {
    dropDownsData['tech_group'] = result[2]?.value?.response.tech_group.map((i, index) => {
      return { id: index + 1, value: i };
    });
  }
  if (result[3].status === 'fulfilled' && result[4].status === 'fulfilled') {
    dropDownsData['engine_type'] = result[3]?.value?.data.miscEngines;
    dropDownsData['vessel_type'] = result[3]?.value?.data.vesselTypes;

    const tempOwnerList = result[3]?.value?.data.owners || [];
    const vesselOwnershipData = result[4]?.value || [];
    dropDownsData['owner'] = [
      {
      id: 0,
      value: 'All Owners',
      },
      ...generateOwnerListWithOwnershipIds(tempOwnerList, vesselOwnershipData),
    ];
  }
  if (result[4].status === 'fulfilled') {
    dropDownsData['planned_vessel'] = result[4]?.value
      .map((i) => {
        return { id: i.vessel_id, value: i.name };
      })
      .filter(
        (e) => e.value !== undefined && e.value !== null && e.id !== undefined && e.id !== null,
      );
  }
  dropDownsData['planned_vessel'] = [
    {
      id: 0,
      value: 'All Vessels',
    },
    ...dropDownsData['planned_vessel'],
  ];
  dropDownsData['previous_vessel'] = dropDownsData['planned_vessel'];
  dropDownsData['bool'] = bool;
  dropDownsData['gender'] = gender;
  dropDownsData['dataQuality'] = dataQuality;
  dropDownsData['with_fml_vessel_experience'] = with_fml_vessel_experience;
  dropDownsData['vessel_size'] = vessel_size;
  dropDownsData['docs_in_hand'] = docs_in_hand;
  dropDownsData['previous_tech_group'] = dropDownsData['tech_group'];
  dropDownsData['previous_rank'] = dropDownsData['ranks'];
  dropDownsData[screeningFilterKeys.SCREENING_FOR] = screenForColumnValues;
  dropDownsData[screeningFilterKeys.PENDING_APPROVAL_GROUP] = pendingApprovalGroupColumnValues;

  dropDownsData = {
    ...dropDownsData,
    years_of_long_service_type: yearsOfLongServiceType,
  };
  dropDownsData['engine_type'] = [
    ...dropDownsData['engine_type'],
    {
      id: 0,
      value: 'All',
    },
  ];
  dropDownsData['vessel_type'] = [
    ...dropDownsData['vessel_type'],
    {
      id: 0,
      value: 'All Vessel Types',
    },
  ];

  dropDownsData['ranks'] = [
    ...dropDownsData['ranks'],
    {
      id: 0,
      value: 'All Ranks',
    },
  ];
  dropDownsData['tech_group'] = [
    ...dropDownsData['tech_group'],
    {
      id: 0,
      value: 'All Tech Group',
    },
  ];
  return [
    'nationalities',
    'countries',
    'ranks',
    'offices',
    'gender',
    'tech_group',
    'engine_type',
    'vessel_type',
    'with_fml_vessel_experience',
    'vessel_size',
    'docs_in_hand',
    'previous_rank',
    'previous_tech_group',
    'planned_vessel',
    'previous_vessel',
    'years_of_long_service_type',
    'owner',
    screeningFilterKeys.SCREENING_FOR,
    screeningFilterKeys.PENDING_APPROVAL_GROUP,
  ].reduce(
    (map, key) => ({
      ...map,
      [key]: dropDownsData[key] || [],
    }),
    {},
  );
};

const loadSeafarerReportPageDropDownData = async () => {
  const result = await Promise.allSettled([
    seafarerService.getTechGroupDropDown(),
    vesselService.queryVesselOwnership(
      'order=created_at+desc&flatten=true&f=vessel.id&f=owner.ship_party_id&f=owner.value&f=vessel.ref_id',
    ),
    seafarerService.getSeafarerDropDownData(),
    seafarerService.getSeafarerReportingOfficeDropDownData(),
    seafarerService.getShipPartyOwnerList(),
  ]);
  let dropDownsData = {
    tech_group: [],
    vessel: [],
    [reportFilterKeys.planned_wages_unit]: AccountingCurrency,
    ownership_details: [],
  };

  if (result[0].status === 'fulfilled') {
    dropDownsData['tech_group'] = result[0]?.value?.response.tech_group.map((i, index) => {
      return { id: index + 1, value: i };
    });
  }
  if (result[1].status === 'fulfilled') {
    dropDownsData['vessel'] = result[1]?.value
      .map((i) => {
        return { id: i.id, value: i.name };
      })
      .filter(
        (e) => e.value !== undefined && e.value !== null && e.id !== undefined && e.id !== null,
      );
    dropDownsData['vessel_with_id'] = result[1]?.value
      .map((i) => {
        return { id: i.vessel_id, value: i.name, ref_id: i.ref_id };
      })
      .filter(
        (e) => e.value !== undefined && e.value !== null && e.id !== undefined && e.id !== null,
      );
    dropDownsData['ownership_details'] = result[1]?.value;
  }
  if (result[2].status === 'fulfilled') {
    dropDownsData[reportFilterKeys.reports_ranks] = [
      {
        id: 0,
        value: 'All Ranks',
      },
      ...(result[2]?.value?.ranks || []),
    ];
    dropDownsData[reportFilterKeys.reports_nationalities] = [
      {
        id: 0,
        value: 'All',
      },
      ...(result[2]?.value.nationalities || []),
    ];
  }
  if (result[3].status === 'fulfilled') {
    dropDownsData[reportFilterKeys.reporting_offices] = [
      {
        id: 0,
        value: 'All Reporting Offices',
      },
      ...(result[3]?.value || []),
    ];
  }
  if (result[4].status === 'fulfilled') {
    dropDownsData[reportFilterKeys.report_owners] = [
      {
        id: 0,
        value: 'All Owners',
      },
      ...(result[4]?.value?.results || []).map((owner) => ({
        id: owner.id, // owner.id is the ship_party_id
        value: owner.name,
      })),
    ];
  }

  dropDownsData['vessel'] = [
    {
      id: 0,
      value: 'All Vessels',
    },
    ...dropDownsData['vessel'],
  ];
  dropDownsData[reportFilterKeys.reports_vessel] = [
    {
      id: 0,
      value: 'All Vessels',
    },
    ...dropDownsData['vessel'],
  ];
  dropDownsData[reportFilterKeys.vessel_with_id] = [
    {
      id: 0,
      value: 'All Vessels',
    },
    ...dropDownsData['vessel_with_id'],
  ];
  dropDownsData['tech_group'] = [
    ...dropDownsData['tech_group'],
    {
      id: 0,
      value: 'All Tech Group',
    },
  ];

  dropDownsData[reportFilterKeys.recommended] = [
    {
      id: 0,
      value: 'All',
    },
    {
      id: 1,
      value: recommendationStatus.WITHOUT_DEVIATION,
    },
    {
      id: 2,
      value: recommendationStatus.WITH_DEVIATION,
    },
  ];
  dropDownsData[reportFilterKeys.approval_status] = [
    {
      id: 0,
      value: 'All Status',
    },
    {
      id: 1,
      value: approvalStatus.APPROVED,
    },
    {
      id: 2,
      value: approvalStatus.REJECTED,
    },
    {
      id: 3,
      value: approvalStatus.PENDING,
    },
  ];
  return [
    reportFilterKeys.tech_group,
    reportFilterKeys.vessel,
    reportFilterKeys.reports_vessel,
    reportFilterKeys.reports_ranks,
    reportFilterKeys.reports_nationalities,
    reportFilterKeys.recommended,
    reportFilterKeys.approval_status,
    reportFilterKeys.reporting_offices,
    reportFilterKeys.report_owners,
    reportFilterKeys.planned_wages_unit,
    reportFilterKeys.ownership_details,
    reportFilterKeys.vessel_with_id,
  ].reduce(
    (map, key) => ({
      ...map,
      [key]: dropDownsData[key] || [],
    }),
    {},
  );
};
class SearchController {
  async onLoadPage(page = SEAFARER_PAGE) {
    let dropDownData = {};
    const sortData = (type, sortBy = 'value') => {
      const data = dropDownData[type] || [];
      if (sortBy === 'id') {
        return data.sort((a, b) => a.id - b.id);
      }
      return data.sort((a, b) =>
        a.value.toLowerCase() > b.value.toLowerCase() ? 1 : -1,
      );
    };

    if (page === SEAFARER_PAGE) {
      dropDownData = await loadSeafarerPageDropDownData();
      dropDownData['nationalities'] = sortData('nationalities');
      dropDownData['nationalities'] = [
        ...dropDownData['nationalities'],
        {
          id: 0,
          value: 'All',
        },
      ];
      dropDownData['offices'] = sortData('offices');
      dropDownData['offices'] = [
        ...dropDownData['offices'],
        {
          id: 0,
          value: 'All',
          ship_part_id: 0,
        },
      ];
      const sortedRanks = sortData('ranks', 'id');
      dropDownData['target_rank'] = sortedRanks;
      dropDownData['sign_on_ranks'] = sortedRanks;
      dropDownData['bool'] = bool;
      dropDownData['gender'] = gender;
      dropDownData['dataQuality'] = dataQuality;
      dropDownData['tech_group'] = sortData('tech_group');
      dropDownData['target_vessel_type'] = sortData('vessel_type');
      dropDownData['engine_type'] = sortData('engine_type');
      dropDownData['vessel'] = dropDownData['planned_vessel'];

      const accountStatusJsonEntries = Object.entries(accountStatusJson);
      const journeyStatusJsonEntries = Object.entries(journeyStatusJson);
      const examStatusJsonEntries = Object.entries(examStatusJson);

      dropDownData['account_status'] = accountStatusJsonEntries
        .map((i) => {
          return { id: i[0], value: i[1].name };
        })
        .sort((a, b) => (a.value.toLowerCase() > b.value.toLowerCase() ? 1 : -1));
      dropDownData['journey_status'] = journeyStatusJsonEntries
        .map((i) => {
          return { id: i[0], value: i[1].name };
        })
        .sort((a, b) => (a.value.toLowerCase() > b.value.toLowerCase() ? 1 : -1));
      dropDownData['exam_status'] = examStatusJsonEntries
        .map((i) => {
          return { id: i[0], value: i[1].name };
        })
        .sort((a, b) => (a.value.toLowerCase() > b.value.toLowerCase() ? 1 : -1));

      dropDownData['plan_status'] = dropDownData['journey_status'].filter((i) =>
        [
          'recommended',
          'recommended_with_deviation',
          'crew_assignment_approved',
          'travelling',
        ].includes(i.id),
      );
    }
    if (page === SEAFARER_REPORT_PAGE) {
      dropDownData = await loadSeafarerReportPageDropDownData();
      dropDownData['vessel'] = sortData('vessel');
      dropDownData['tech_group'] = sortData('tech_group');
      dropDownData['reports_nationalities'] = sortData('reports_nationalities');
      dropDownData['reports_ranks'] = sortData('reports_ranks', 'id');
    }
    return {
      dropDownData,
    };
  }

  getAllFilters(page = SEAFARER_PAGE) {
    return searchQuery.getSearchTypes(page);
  }

  getType(element, page = SEAFARER_PAGE) {
    const found = searchQuery.getSearchTypes(page).find(function (item) {
      return item.queryKey === element.queryKey;
    });

    return found;
  }

  getQuery(filters, currentTab = null) {
    return searchQuery.mapSearchCriteriaToQueryString(filters, currentTab);
  }
}

export default SearchController;
