/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/state-in-constructor */
/* eslint-disable react/prop-types */
/* eslint-disable no-console */
import React from 'react';
import { <PERSON><PERSON>er<PERSON><PERSON>er, Route, Switch, Redirect } from 'react-router-dom';
import * as localization from '@paris2/localization';
import { I18nextProvider, I18nextProviderProps } from 'react-i18next';
import GA4React from 'ga-4-react';
import { ToastContainer } from 'react-toastify';
import AddSeafarerPage from './pages/AddSeafarerPage';
import DetailsPage from './pages/Details';
import WorldCheckOnePage from './pages/WorldCheckOnePage';
import PdfDocViewer from './pages/PdfViewer';
import ListPage from './pages/List';
import ScreeningHistoryPage from './pages/ScreeningHistoryPage';
import './seafarer.scss';
import userService from './service/user-service';
import UserRoleController from './controller/user-role-controller';
import Spinner from './component/common/Spinner';
import ErrorAlert from './component/common/ErrorAlert';
import { ErrorPage } from './styleGuide';
import CrewListPage from './pages/CrewListPage';
import AddPreJoiningDetails from './pages/AddPreJoiningDetails';
import SeafarerReportsPage from './pages/SeafarerReports';
import { FAQ as Faq } from './pages/FAQ';
import { ListTabData } from './model/TabData';
import AddSeafarerBankAccounts from './component/AddSeafarer/AddSeafarerBankAccounts';
import VesselPlanPage from './pages/report/modeller/VesselPlanPage';
import VesselPlanEditPage from './pages/report/modeller/VesselPlanEditPage';
import DailyNews from './pages/daily-news/daily-news';
import SeafarerRecommendation from './pages/seafarer-recommendation/seafarer-recommendation';
import SeafarerBioData from './pages/seafarer-bio-data/seafarer-bio-data';
import MasterAppraisal from './pages/MasterAppraisal';
import ManagedVesselsPage from './pages/ManagedVesselsPage';
import CrewPlannerPage from './pages/CrewPlannerPage';
import CrewManagementPage from './pages/CrewManagementPage';
import AdminAccess from './pages/AdminAccess';
import { AccessProvider } from './component/common/Access';
import 'react-toastify/dist/ReactToastify.css';

const userRoleController = new UserRoleController();

interface Languages {
  key: string;
  name: string;
}

interface State {
  hasError: boolean;
  i18n: I18nextProviderProps | null;
  languages: Languages[];
  currentLanguage: string | null;
  roleConfig: unknown;
}

export interface RootProps {
  kc: Keycloak;
  ga4react: GA4React;
}

export interface Keycloak {
  realmAccess: RealmAccess;
  tokenParsed: any;
}

interface RealmAccess {
  roles: string[];
}

class Root extends React.Component<RootProps, State> {
  state = {
    hasError: false,
    i18n: null,
    languages: [],
    currentLanguage: null,
    roleConfig: null,
    userProfile: null,
  };

  componentDidCatch(error, info) {
    this.setState({ hasError: true });
  }

  onLanguageChanged = async ({ i18n, languages, currentLanguage }) => {
    this.setState((prevState) => ({
      ...prevState,
      languages,
      currentLanguage,
      i18n,
    }));
  };

  prepareRoleConfig = async () => {
    if (!this.state.roleConfig) {
      const roleConfigJson = await userRoleController.getConfig(this.props.kc);
      this.setState((prevState) => ({ ...prevState, roleConfig: roleConfigJson }));
    }
  };

  componentDidMount = async () => {
    await userService.init();
    localization.addCallback(this.onLanguageChanged);
    await localization.initialize();
    await this.prepareRoleConfig();
    const user = await this.props.kc.loadUserProfile();
    this.setState((prevState) => ({ ...prevState, userProfile: user }));
  }

  componentWillUnmount = async () => {
    localization.removeCallback(this.onLanguageChanged);
  }

  componentDidUpdate = async () => {
    await userService.init();
    await this.prepareRoleConfig();
  }

  render() {
    const { hasError, i18n, currentLanguage, roleConfig, userProfile } = this.state;
    if (i18n && currentLanguage && roleConfig && userProfile) {
      return (
        <I18nextProvider i18n={i18n}>
          <AccessProvider config={roleConfig} profile={userProfile}>
            <BrowserRouter>
              <Switch>
                <Route exact path="/seafarer/faq/:cmsSharepointSiteID">
                  <Faq />
                </Route>

                <Route exact path="/seafarer">
                  <Redirect to="/seafarer/passed" />
                </Route>
                <Route exact path="/seafarer/:seafarerId/recommendation">
                  <SeafarerRecommendation ga4react={this.props.ga4react} />
                </Route>
                <Route exact path="/seafarer/:seafarerPersonId/bio-data">
                  <SeafarerBioData />
                </Route>

                <Route exact path="/seafarer/daily-news">
                  <DailyNews ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer/screening-history/:seafarerId">
                  <ScreeningHistoryPage />
                </Route>

                <Route
                  exact
                  path={`/seafarer/:tab(${ListTabData.map((i) => i.eventKey).join(
                    '|',
                  )})/:seafarerId?`}
                >
                  <ListPage ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer-reports/:tab">
                  <SeafarerReportsPage ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer-reports">
                  <Redirect to="/seafarer-reports/modeller" />
                </Route>

                <Route exact path="/seafarer-reports/modeller/:vesselOwnerShipId/:action(copy)?">
                  <VesselPlanPage ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer-reports/modeller/:vesselOwnerShipId/edit">
                  <VesselPlanEditPage ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer/:seafarerId?/add/:step?">
                  <AddSeafarerPage
                    keycloak={this.props.kc.tokenParsed}
                    ga4react={this.props.ga4react}
                  />
                </Route>

                <Route exact path="/seafarer/details/:seafarerId/edit-bank-accounts">
                  <AddSeafarerBankAccounts />
                </Route>
                <Route exact path="/seafarer/details/:seafarerId/pre-joining/add">
                  <AddPreJoiningDetails ga4react={this.props.ga4react} />
                </Route>

                <Route
                  exact
                  path="/seafarer/details/:seafarerId/appraisals/mstr-appraisal/view/:mstrAppraisalId"
                >
                  <MasterAppraisal ga4react={this.props.ga4react} />
                </Route>

                <Route
                  exact
                  path="/seafarer/details/:seafarerId/:step?/:documentType?/:action?/:documentId?"
                >
                  <DetailsPage ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer/details/:seafarerId">
                  <Redirect to="/seafarer/details/:seafarerId/general" />
                </Route>

                <Route exact path="/seafarer/crew-list/vessel/:vesselIdParam/:date?">
                  <CrewListPage ga4react={this.props.ga4react} />
                </Route>

                <Route exact path="/seafarer/details/:seafarerId/wco">
                  <WorldCheckOnePage />
                </Route>
                <Route exact path="/seafarer/document/:docId/:docType">
                  <PdfDocViewer />
                </Route>
                <Route exact path="/seafarer/crew-planner/planner/:tab?">
                  <CrewPlannerPage
                    keycloak={this.props.kc.tokenParsed}
                    ga4react={this.props.ga4react}
                  />
                </Route>

                <Route exact path="/seafarer/crew-planner/managed-vessel">
                  <ManagedVesselsPage ga4react={this.props.ga4react} />
                </Route>
                <Route exact path="/seafarer/crew-planner/manage-access">
                  <AdminAccess ga4react={this.props.ga4react} />
                </Route>
                <Route exact path="/seafarer/crew-planner/vessel/:vesselOwnershipId">
                  <CrewManagementPage ga4react={this.props.ga4react} />
                </Route>

                <Route>
                  <ErrorPage errorCode={404} />
                </Route>
              </Switch>
            </BrowserRouter>
          </AccessProvider>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
          />
        </I18nextProvider>
      );
    }
    if (hasError) {
      return <ErrorAlert message="Error occured while loading seafarer!" />;
    }
    return <Spinner />;
  }
}

export { Root };
