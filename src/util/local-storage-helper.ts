import md5 from 'blueimp-md5';

import {
  AVAILABLE_SEAFARERS,
  SEAFARERS_TO_RELIEVE,
  ON_LEAVE_SEAFARER,
} from '../constants/crewPlanner';
import items, { getDefaultScreeningColumns } from '../component/seafarerList/MenuList';
import {
  contractExpiryColumns,
  availableSeafarerColumn,
  getCrewListColumns,
} from '../component/CrewList/MenuList';
import {
  LOCAL_STORAGE_FIELDS,
  seafarerStatus,
  screeningStatus as tabKeys,
  SCREENING_STATUS,
} from '../model/constants';

export const setLocalStorage = (key, value) => {
  localStorage.setItem(key, JSON.stringify(value));
};

export const getLocalStorage = (key) => {
  if (!localStorage.getItem(key)) return undefined;
  return JSON.parse(localStorage.getItem(key));
};

export const isKeyStored = (key, tab, sub_key) => {
  const value = getLocalStorage(key);
  if (
    value &&
    [
      LOCAL_STORAGE_FIELDS.tableSelectedColumns,
      LOCAL_STORAGE_FIELDS.contractTableSelectedColumns,
      LOCAL_STORAGE_FIELDS.availableTableSelectedColumns,
      LOCAL_STORAGE_FIELDS.signedOnTableSelectedColumns,
      LOCAL_STORAGE_FIELDS.recommendedTableSelectedColumns,
    ].includes(sub_key)
  ) {
    return !!value[sub_key];
  }
  if (!value) return false;
  if (!value[tab]) return false;
  return !!value[tab][sub_key];
};
const getNameFlag = (tab) =>
  ![
    'contract-expiry',
    AVAILABLE_SEAFARERS,
    ON_LEAVE_SEAFARER,
    SEAFARERS_TO_RELIEVE,
    'crew-list',
  ].includes(tab);

const getLocalStorageTable = (tab) => {
  return ![
    'contract-expiry',
    'available-seafarers',
    seafarerStatus.SIGNED_ON,
    seafarerStatus.RECOMMENDED,
  ].includes(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
};

const getLocalStorageColumns = (tab) => {
  return ![
    'contract-expiry',
    'available-seafarers',
    seafarerStatus.SIGNED_ON,
    seafarerStatus.RECOMMENDED,
  ].includes(tab)
    ? LOCAL_STORAGE_FIELDS.tableSelectedColumns
    : `${tab}-table-selected-columns`;
};

export const storeColumns = (tab, cols) => {
  const storage_cols = [];
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;

  const localStorageColumns = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.tableSelectedColumns
    : `${tab}-table-selected-columns`;

  cols.forEach((item) => {
    storage_cols.push(item.Header);
  });
  const cur_obj = getLocalStorage(localStorageTable) || {};
  setLocalStorage(localStorageTable, {
    ...cur_obj,
    [localStorageColumns]: storage_cols,
  });
};

export const getStoredColumnHeaders = (tab) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;

  const localStorageColumns = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.tableSelectedColumns
    : `${tab}-table-selected-columns`;

  return getLocalStorage(localStorageTable)[localStorageColumns];
};

export const retrieveColumns = (tab, enableOcimf = false) => {
  const selected_cols = [];
  let menuItems;
  const selected_col_heads = getStoredColumnHeaders(tab);
  switch (true) {
    case tab === 'contract-expiry':
      menuItems = contractExpiryColumns;
      break;
    case tab === AVAILABLE_SEAFARERS:
      menuItems = availableSeafarerColumn;
      break;
    case tab === seafarerStatus.SIGNED_ON:
      menuItems = getCrewListColumns(enableOcimf);
      break;
    case tab === seafarerStatus.RECOMMENDED:
      menuItems = getCrewListColumns();
      break;
    case SCREENING_STATUS.includes(tab):
      menuItems = [
        ...items.slice(1, 7),
        ...getDefaultScreeningColumns(tab),
        ...items.slice(7, 11),
        items[12],
      ];
      break;
    default:
      menuItems = items;
      break;
  }

  menuItems.forEach((col) => {
    if (col.Header && selected_col_heads.includes(col.Header)) selected_cols.push(col);
  });
  return selected_cols;
};

export const modifyTabData = (col, value, tab_obj) => {
  return { ...tab_obj, [col]: value };
};
export const storePageNumber = (tab, page_no) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  const cur_obj = getLocalStorage(localStorageTable) || {};
  setLocalStorage(localStorageTable, {
    ...cur_obj,
    [tab]: modifyTabData(LOCAL_STORAGE_FIELDS.tablePageIndex, page_no, cur_obj[tab]),
  });
};
export const storePageSize = (tab, pageSize) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  const cur_obj = getLocalStorage(localStorageTable) || {};
  setLocalStorage(localStorageTable, {
    ...cur_obj,
    [tab]: modifyTabData(LOCAL_STORAGE_FIELDS.tablePageSize, pageSize, cur_obj[tab]),
  });
};

export const storePageSort = (tab, pageSort) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  const pageSortDefaultContractExpiryObj = {
    id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
    desc: false,
  };
  const pageSortDefaultRankPriorityObj = {
    id: 'seafarer_person:seafarer_status_history:seafarer_rank.sortpriority',
    desc: false,
  };
  const pageSortDefaultAvailabilityDateObj = {
    id: 'seafarer_contact_log.availability_date',
    desc: false,
  };
  if (tab === 'contract-expiry' && pageSort.length === 0) {
    pageSort.push(pageSortDefaultContractExpiryObj);
  } else if (
    [seafarerStatus.SIGNED_ON, seafarerStatus.RECOMMENDED].includes(tab) &&
    pageSort.length === 0
  ) {
    pageSort.push(pageSortDefaultRankPriorityObj);
  } else if (tab === SEAFARERS_TO_RELIEVE && pageSort.length === 0) {
    pageSort.push(pageSortDefaultContractExpiryObj);
  } else if (tab === ON_LEAVE_SEAFARER && pageSort.length === 0) {
    pageSort.push(pageSortDefaultAvailabilityDateObj);
  }

  const cur_obj = getLocalStorage(localStorageTable) || {};
  setLocalStorage(localStorageTable, {
    ...cur_obj,
    [LOCAL_STORAGE_FIELDS.tablePageSort]: pageSort,
  });
};

// Reset page number to 0 on all tabs
export const resetAllTabs = () => {
  Object.values(tabKeys).forEach((tab) => {
    storePageNumber(tab, 0);
  });
};
export const getPageSize = (tab) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  return Number(getLocalStorage(localStorageTable)[tab][LOCAL_STORAGE_FIELDS.tablePageSize]);
};

export const getPageNumber = (tab) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  return Number(getLocalStorage(localStorageTable)[tab][LOCAL_STORAGE_FIELDS.tablePageIndex]);
};

export const getPageSort = (tab) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  const table_obj = getLocalStorage(localStorageTable);
  return table_obj ? table_obj[LOCAL_STORAGE_FIELDS.tablePageSort] || [] : [];
};

export const getPageTableState = (tab, defaultPageSize = 10) => {
  const localStorageTable = getNameFlag(tab)
    ? LOCAL_STORAGE_FIELDS.masterKey
    : `${tab}-table-details`;
  const pageSize = isKeyStored(localStorageTable, tab, LOCAL_STORAGE_FIELDS.tablePageSize)
    ? getPageSize(tab)
    : defaultPageSize;
  const pageIndex = isKeyStored(localStorageTable, tab, LOCAL_STORAGE_FIELDS.tablePageIndex)
    ? getPageNumber(tab)
    : 0;

  return { pageSize, pageIndex };
};

export const storeQuery = (query) => {
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  setLocalStorage(LOCAL_STORAGE_FIELDS.masterKey, {
    ...cur_obj,
    [LOCAL_STORAGE_FIELDS.advancedSearchParams]: query,
  });
};

export const getQuery = () => {
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  return cur_obj[LOCAL_STORAGE_FIELDS.advancedSearchParams] || '';
};

export const genLocalStorageControlFuncs = (localStorageKeys = [], version = 1) => {
  const hashValue = md5([...localStorageKeys, version].join('-'));
  const cacheTimeKey = `DropDownDataCachedTime_${version}_${hashValue}`;

  const getItemKey = (key) => {
    return `${key}_${version}_${hashValue}`;
  };

  const removeLocalStorageItemsIfExpired = async () => {
    let isCacheInvalidated = false;
    const limitHours = 10;
    const now = new Date();
    const cachedValue = localStorage.getItem(cacheTimeKey);
    if (!cachedValue) {
      isCacheInvalidated = true;
      return isCacheInvalidated;
    }

    if (cachedValue) {
      const cachedTime = JSON.parse(cachedValue);
      const cachedDate = new Date(cachedTime);
      let diff = now.getTime() - cachedDate.getTime();
      diff = diff / 1000 / 60 / 60;
      if (diff >= limitHours) {
        localStorageKeys.forEach((localStorageKey) => {
          localStorage.removeItem(getItemKey(localStorageKey));
        });
        localStorage.removeItem(cacheTimeKey);

        isCacheInvalidated = true;
        return isCacheInvalidated;
      }
    }

    return isCacheInvalidated;
  };

  const setLocalStorageItem = (key, value) => {
    if (!localStorageKeys.includes(key)) {
      throw Error(`key ${key} is not allowed`);
    }

    localStorage.setItem(getItemKey(key), value);
    localStorage.setItem(cacheTimeKey, JSON.stringify(new Date()));
  };

  const getLocalStorageItem = (key) => {
    if (!localStorageKeys.includes(key)) {
      throw Error(`key ${key} is not allowed`);
    }

    return localStorage.getItem(getItemKey(key));
  };

  return { removeLocalStorageItemsIfExpired, getLocalStorageItem, setLocalStorageItem };
};
