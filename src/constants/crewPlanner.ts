export const SEAFARERS_TO_RELIEVE = 'seafarers-to-relieve';
export const AVAILABLE_SEAFARERS = 'available-seafarers';
export const ON_LEAVE_SEAFARER = 'on-leave-seafarers';
export const ListTabData = [
  {
    eventKey: SEAFARERS_TO_RELIEVE,
    tabName: 'Due Relieve Seafarer',
    dataTestId: SEAFARERS_TO_RELIEVE,
  },
  {
    eventKey: ON_LEAVE_SEAFARER,
    tabName: 'On Leave Seafarer',
    dataTestId: ON_LEAVE_SEAFARER,
  },
];

export const STATUS = {
  unplanned: 'unplanned',
  unclubbed: 'unclubbed',
  clubbed: 'clubbed',
  rejected: 'rejected',
  club_confirmed: 'club_confirmed',
};

export const NAME_STATUS_MAPPING = {
  [STATUS.unplanned]: 'Unplanned',
  [STATUS.unclubbed]: 'Unclubbed',
  [STATUS.clubbed]: 'Clubbed',
  [STATUS.rejected]: 'Rejected',
  [STATUS.club_confirmed]: 'Club Confirmed',
};

export enum CREW_PLANNING_STATUS {
  unclubbed = 1,
  canceled = 2,
  rejected = 3,
  clubbed = 4,
  club_confirmed = 5,
  completed = 6,
  expired = 7,
}

export enum CREW_PLANNING_TYPE {
  relieve = 1,
  add_rank = 2,
  promotion = 3,
  offsigner = 4,
  missing_personnel = 5,
}

export const CREW_PLANNING_LEFT_SIDE = [
  CREW_PLANNING_STATUS.unclubbed,
  CREW_PLANNING_STATUS.canceled,
  CREW_PLANNING_STATUS.rejected,
];
export const CREW_PLANNING_RIGHT_SIDE = [
  CREW_PLANNING_STATUS.clubbed,
  CREW_PLANNING_STATUS.club_confirmed,
  CREW_PLANNING_STATUS.completed,
  CREW_PLANNING_STATUS.expired,
];

export const CREW_PLANNING_ALLOW_UNCLUB = [
  CREW_PLANNING_STATUS.clubbed,
  CREW_PLANNING_STATUS.club_confirmed,
];

export const CREW_PLANNING_NON_ACTIONABLE_STATUS = [
  CREW_PLANNING_STATUS.completed,
  CREW_PLANNING_STATUS.expired,
];

export const RANK_HIERARCHY: Record<string, string> = {
  'MASTER': 'CHIEF OFFICER',
  'CHIEF OFFICER': 'MASTER',
  'CHIEF ENGINEER': '2ND ENGINEER',
  '2ND ENGINEER': 'CHIEF ENGINEER',
  '2ND OFFICER': '3RD OFFICER',
  '3RD OFFICER': '2ND OFFICER',
  '3RD ENGINEER': '4TH ENGINEER',
  '4TH ENGINEER': '3RD ENGINEER',
};

export const CREW_PLANNING_STATUS_MAPPING: { [key: string]: string[] } = {
  Unplanned: ['unplanned'],
  Unclubbed: ['unclubbed'],
  Clubbed: ['clubbed,club_confirmed']
}

export const CREW_PLANNING_STATUSES = {
  UNPLANNED: 'unplanned',
  CLUBBED: 'clubbed',
  CLUB_CONFIRMED: 'club_confirmed',
  UNCLUBBED: 'unclubbed'
}

export const SECOND_TABLE_FILTERS: { [key: string]: string } = {
  suggested_seafarers: "Suggested Seafarers",
  available_seafarers: "Available Seafarers"
}

export const OPERATION_CANCELLED_ERROR_TEXT = 'Operation canceled due to new request.'

export const DOCUMENT_SCORE_GRADE = {
  Pass: 'Pass',
  Fail: 'Fail'
}