import moment from 'moment';
import defaultFilters from '../../util/advance-search/advance-search-default-filter';

jest.mock('../../util/advance-search/search-types', () => () => [
  { type: 'data_of_contract_expry' },
  { type: 'sign_on_ranks' },
  { type: 'tech_group' },
  { type: 'offices' },
  { type: 'vessel' },
  { type: 'owner' },
  { type: 'nationalities' },
  { type: 'available_date' },
  { type: 'account_status' },
  { type: 'target_rank' },
  { type: 'with_fml_vessel_experience' },
  { type: 'target_vessel_type' },
  { type: 'duration_with_company' },
  { type: 'engine_type' },
  { type: 'duration_in_target_rank' },
  { type: 'vessel_size' },
  { type: 'duration_on_target_vessel_type' },
]);

describe('defaultFilters', () => {
  it('should return correct filters for "contract-expiry" with approximate date check', () => {
    const filters = defaultFilters('contract-expiry');

    // Define the current date and 3 months in the future for date approximation
    const currentDate = moment();
    const threeMonthsFutureDate = moment().add(3, 'months');

    expect(filters).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          type: { type: 'data_of_contract_expry' },
          subtype: {
            startDate: expect.any(Date),
            endDate: expect.any(Date),
          },
          defaultTab: 'contract-expiry',
        }),
        expect.objectContaining({
          type: { type: 'sign_on_ranks' },
          defaultTab: 'contract-expiry',
        }),
        // Additional checks for other filters as needed...
      ])
    );

    // Ensure the dates fall within the expected range
    const contractExpiryFilter = filters.find(f => f.type.type === 'data_of_contract_expry');
    expect(moment(contractExpiryFilter.subtype.startDate).isSame(currentDate, 'day')).toBe(true);
    expect(moment(contractExpiryFilter.subtype.endDate).isSame(threeMonthsFutureDate, 'day')).toBe(true);
  });

  it('should return correct filters for "available-seafarers" with approximate date check', () => {
    const filters = defaultFilters('available-seafarers');

    // Define the approximate date 2 months in the past
    const twoMonthsPastDate = moment().subtract(2, 'months').startOf('day');

    expect(filters).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          type: { type: 'available_date' },
          subtype: expect.any(Date),
          defaultTab: 'available-seafarers',
        }),
        expect.objectContaining({
          type: { type: 'account_status' },
          defaultTab: 'available-seafarers',
        }),
        // Additional checks for other filters as needed...
      ])
    );

    // Ensure the available date falls within the expected range
    const availableDateFilter = filters.find(f => f.type.type === 'available_date');
    expect(moment(availableDateFilter.subtype).isSame(twoMonthsPastDate, 'day')).toBe(true);
  });
});
