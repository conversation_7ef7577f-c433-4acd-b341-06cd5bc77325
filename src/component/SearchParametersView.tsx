import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import moment from 'moment';

const SearchParametersView = (props) => {
  const handleEditClick = () => {
    props.setShowAdvSearchMobile(true);
  };

  function getParameters() {
    const data = props.criteria;
    const result = data.map((item) => {
      const title = item.type.name;
      let value = '';
      switch (item.type.inputType) {
        case 'dropdown':
          value = (item.subtype.length ? item.subtype : []).map((i) => `${i.value}`).join(' or ');
          break;
        case 'checkbox':
          value = item.subtype.value;
          break;
        case 'text':
          value = item.subtype;
          break;
        case 'number_range':
          value = `${item.subtype.min} to ${item.subtype.max}`;
          break;
        case 'date':
          value = `from ${moment(item.subtype.startDate).format('DD MMM YYYY')} to ${moment(item.subtype.endDate).format('DD MMM YYYY')}`;
          break;
        case 'year':
          value = moment(item.subtype).format('YYYY');
          break;
        case 'date_single':
          value = item.subtype ? moment(item.subtype).format('YYYY-MM-DD') : '';
          break;
        default:
          value = item.subtype ?? '';
      }

      return `${title}: ${value}`;
    });

    return result;
  }

  return (
    <Container className="search-parameters-wrapper">
      <Row className={getParameters().length === 0 ? 'hidden' : null}>
        <Col md={9}>
          <div className="search-parameters-wrapper__title">Search by</div>
          <div className="search-parameters-wrapper__params">
            {getParameters().map((element, index) => (
              <span key={element}>
                <span>{element}</span>
                {index != getParameters().length - 1 ? (
                  <span className="search-parameters-wrapper__slash"> / </span>
                ) : null}
              </span>
            ))}
          </div>
        </Col>
        <Col md={3} className="my-auto search-parameters-wrapper__button-wrapper">
          <Button variant="primary" size="sm" onClick={handleEditClick}>
            Edit Search Criteria
          </Button>
        </Col>
      </Row>
    </Container>
  );
};

export default SearchParametersView;
