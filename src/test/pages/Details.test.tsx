import React from 'react';
import '@testing-library/jest-dom';
import { Route, Router } from 'react-router-dom';
import { waitFor, screen, fireEvent, act, render, within } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { AccessProvider } from '@src/component/common/Access';
import Details, { createErrorObject } from '../../pages/Details';
import { updateWrapper } from '../../setupTests';
import seafarerService from '../../service/seafarer-service';
import seafarerSurveryService from '../../service/seafarer-survery-service';
import * as referenceService from '../../service/reference-service';
import {
  getMockedSeafarerResponse,
  getMockedParentSeafarer,
  getMockedChildSeafarer,
  getMockedSeafarerResponseWithoutError,
  getMockedSeafarerExperience,
  getMockedSeafarerResponseWithNoContactDetailAccess,
} from '../resources/seafarer-mock-data';
import dropdowndata from '../resources/drop-down-data.json';
import * as mockResponse from '../resources/document-response';
import {
  getCurrentSeafarerStatusHistory,
  getSeafarerStatusHistoryByPersonIDMockResponse,
} from '../resources/seafarer-status-history';
import * as mockRecommendationCheckData from '../resources/recommended-checks-data';
import {
  getSeafarerMockResponse,
  getSeafarerStatusMockResponse,
} from '../resources/get-mock-response-details-page';
import { dateAsString, dateAsDash } from '../../model/utils';
import {
  RECOMMENDATION_INCOMPLETE_TRAINING_REQ,
  RECOMMENDATION_INVESTIGATION_INCOMPLETE_TRAINING_REQ,
} from '../../constants/common-validation-messages';
import { wagesApiResponseWithWagesDetails } from '../resources/wages-api-response-with-wages-details';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/seafarer-service');
jest.mock('../../service/screening-service');

describe('Details Page', () => {
  let wrapper;
  const history = createMemoryHistory();

  const renderDetailsPage = (
    editSeafarer: boolean,
    editBankAccount: boolean = false,
    initialEntry: string = '/seafarer/details/1',
    path: string = '/seafarer/details/:seafarerId',
    editDuplicateHKID: boolean = false,
    viewScreening: boolean = true,
    localHistory = history,
    withRoute = false,
    editRecommendation = false,
  ) =>
    withRoute
      ? render(
        <Router history={localHistory}>
            <AccessProvider
            config={{
                seafarer: {
                  editSeafarer,
                  screening: {
                    view: viewScreening,
                  },
                  create: {},
                  edit: {
                    recommendation: editRecommendation,
                    duplicateHKID: editDuplicateHKID,
                    bankAccount: editBankAccount,
                  },
                  view: {
                    general: true,
                    bankAccount: true,
                    hkid: true,
                  },
                  hidden: {
                    contactDetails: false,
                  },
                },
              }}
          >
            <Route path={path}>
                <Details />
              </Route>
          </AccessProvider>
          </Router>,
      )
      : render(
        <Router history={localHistory}>
            <AccessProvider
            config={{
                seafarer: {
                  editSeafarer,
                  screening: {
                    view: viewScreening,
                  },
                  create: {},
                  edit: {
                    recommendation: editRecommendation,
                    duplicateHKID: editDuplicateHKID,
                    bankAccount: editBankAccount,
                  },
                  view: {
                    general: true,
                    bankAccount: true,
                    hkid: true,
                  },
                  hidden: {
                    contactDetails: false,
                  },
                },
              }}
          >
            <Details />
          </AccessProvider>
          </Router>,
      );

  beforeAll(() => {
    jest.setTimeout(100000);
    seafarerService.getSeafarerStatusHistoryByPersonID = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: getSeafarerStatusHistoryByPersonIDMockResponse() }),
      );
    seafarerService.getRecommendedChecks = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: mockRecommendationCheckData.recommendedChecksData,
      }),
    );
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerResponse() }));
    seafarerService.getChildHKID = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: [] }));
    seafarerService.getParentSeafarerDetails = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: [{ id: 1 }] } }));
    seafarerSurveryService.getSuptAppraisalList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: [] } }));
    Details.getScreeningStatusInfo = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { message: 'This seafarer has passed screening.', type: 'success' },
      }),
    );

    seafarerService.getSeafarerStatus = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getCurrentSeafarerStatusHistory() }));

    seafarerService.getSeafarerExperience = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: [] }));
    seafarerService.getExperienceRankHistory = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: [] }));
    seafarerService.getBankNames = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: [] }));

    seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getSeafarerDocumentDropdown = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getSeafarerReportingOfficeDropDownData = jest
      .fn()
      .mockImplementation(() => [dropdowndata.offices]);
    seafarerService.getSeafarerWages = jest.fn().mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: wagesApiResponseWithWagesDetails,
      }),
    );
    jest
      .spyOn(referenceService, 'getVisaRegionDropDownData')
      .mockImplementation(() => Promise.resolve(mockResponse.visaRegionReferenceApiResponseData));
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  describe.skip('Button Toolbar', () => {
    beforeAll(async () => {
      clickOnActionButton(wrapper);
      await updateWrapper(wrapper);
    });

    it.skip('should have menus in button toolbar', async () => {
      const allLinks = wrapper.find('ButtonToolbar DropdownButton a');
      const links = ['Documents', 'Screening History'];
      expect(allLinks.map((link) => link.text())).toEqual(expect.arrayContaining(links));
    });
  });

  it('should contains 8 tabs', async () => {
    renderDetailsPage(false);
    const expectedTabs = [
      'General',
      'Screening Details',
      'ID Documents',
      'Endorsement & Verification',
      'Other Documents',
      'Bank Accounts',
      'Availability',
      'Pre-Joining',
      'Experience',
      'Appraisals',
      'Status History',
    ];

    await waitFor(() => {
      const tabElements = screen.getAllByRole('tab');

      expect(tabElements.length).toBe(expectedTabs.length);

      tabElements.forEach((tabElement, index) => {
        expect(tabElement.textContent).toBe(expectedTabs[index]);
      });
    });
  });

  it('should have all sections', async () => {
    renderDetailsPage(false);
    const expectedSections = [
      'General Details',
      'Personal Particulars',
      'Contact Details',
      'Documents',
    ];

    await waitFor(() => {
      const sectionElements = screen.getAllByText((content, element) => {
        return expectedSections.includes(content);
      });

      expect(sectionElements.length).toBe(expectedSections.length);

      sectionElements.forEach((sectionElement, index) => {
        expect(sectionElement.textContent).toBe(expectedSections[index]);
      });
    });
  });

  it('should have View Paris 1.0 Detail Page link', async () => {
    renderDetailsPage(false);

    await waitFor(() => {
      const parin1Link = screen.getByRole('link', {
        name: 'View PARIS 1.0 Detail Page',
      });

      expect(parin1Link).toBeInTheDocument();
    });
  });

  it('should show the created on label ', async () => {
    renderDetailsPage(false);

    await waitFor(() => {
      const createdByLabel = screen.getByText('Created on 23 Jul 2020');

      expect(createdByLabel).toBeInTheDocument();
    });
  });

  it('should have all fields', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      false,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
    );

    const contactDetailsFields = ['Telephone Number', 'Mobile Number', 'Email Address', 'Address'];
    const personalParticulars = [
      'Height',
      'Weight',
      'Overall Size',
      'T-shirt Size',
      'Jacket Size',
      'Shoe Size',
      'Smoking',
      'Vegetarian',
      'Nearest Airport',
      'Marital Status',
      'Name of Spouse',
      'Number of Children',
      "Children's Name & DOB",
    ];
    const nextOfKin = [
      // 'Name of Next of Kin',
      'Relationship',
      'Telephone No.',
      'Mobile No.',
      'Email Address',
      'Address of Next of Kin',
      'Percentage',
    ];
    const expectedElements = [
      'Seafarer ID (HKID)',
      'First Name',
      'Middle Name',
      'Last Name',
      'Gender',
      'Date of Birth',
      'Place of Birth',
      'Country of Birth',
      'Nationality',
      'Rank',
      'Reporting Office',
      'Manning Agency',
      'Availability Date',
      'Availability Remark',
      ...personalParticulars,
      ...nextOfKin,
      ...contactDetailsFields,
      'Date of Issue',
      'Date of Expiry',
      'Place of Issue',
      'Country of Issue',
      'Document',
      'Country',
      'Port of Issue',
      'Date of Issue',
      'Date of Expiry',
      'Document',
      'Is Original',
    ];

    await waitFor(() => {
      expectedElements.forEach((e) =>
        expect(renderedView.getAllByText(e).length).toBeGreaterThanOrEqual(1),
      );
    });
  });

  it('should call route /seafarer/details/:id on click of Edit button', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      true,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
      true,
    );

    let editButton: HTMLElement;
    await waitFor(() => {
      editButton = renderedView.getByText('Edit');
    });
    await act(async () => {
      fireEvent.click(editButton);
    });

    await waitFor(() => {
      expect(localHistory.location.pathname).toEqual('/seafarer/1/add/basic');
    });
  });

  it('should call route /seafarer/details/:id/screening on click of Screening Details tab', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      false,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
      true,
    );

    let editButton: HTMLElement;
    await waitFor(() => {
      editButton = renderedView.getByText('Screening Details');
    });
    await act(async () => {
      fireEvent.click(editButton);
    });

    expect(localHistory.location.pathname).toEqual('/seafarer/details/1/screening');
  });

  it('should call route /seafarer/details/:id/status-history on click of statusHistory  tab', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      false,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
      true,
    );
    let editButton: HTMLElement;
    await waitFor(() => {
      editButton = renderedView.getByText('Status History');
    });
    await act(async () => {
      fireEvent.click(editButton);
    });
    expect(localHistory.location.pathname).toEqual('/seafarer/details/1/status-history');
  });

  it('should validate seafarer details on render', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      false,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
    );

    const errorList = [
      'Please select Rank (Basic)',
      'Please select Reporting Office (Basic)',
      'Please enter Place of Birth (Basic / Personal Details)',
      'Please select Country of Birth (Basic / Personal Details)',
      'Please enter a valid Passport Number (Basic / Passport Details)',
      'Please provide a copy of Passport (Basic / Passport Details)',
      "Please provide a copy of Seaman's Book (Basic / Seaman's Book)",
    ];

    await waitFor(() => {
      errorList.forEach((e) =>
        expect(renderedView.getAllByText(e).length).toBeGreaterThanOrEqual(1),
      );
    });
  });

  it('should show multiple email id on render', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      false,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
    );

    const emailList = ['<EMAIL>', '<EMAIL>'];

    await waitFor(() => {
      emailList.forEach((e) => {
        const email = renderedView.getByText(e);
        const row = email.closest('tr');

        expect(row.children[0].textContent).toEqual('Email Address');
      });
    });
  });

  it('should show popup on Change Status click', async () => {
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerResponseWithoutError() }));

    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      true,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
    );

    await waitFor(() => {
      const changeStatusButton = renderedView.getByText('Change Status');

      fireEvent.click(changeStatusButton);

      const modalItem = renderedView.getByRole('dialog');

      expect(modalItem).toBeInTheDocument();

      const confirmButton = renderedView.getByText('Confirm');
      expect(confirmButton).toBeInTheDocument();
    });
  });

  it('should show seafarer status on details page', async () => {
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerResponseWithoutError() }));

    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      true,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
    );

    await waitFor(() => {
      const seafarerStatus = renderedView.getByText('ACTIVE');

      expect(seafarerStatus).toBeInTheDocument();
      expect(seafarerStatus).toHaveClass('seafarer-status');
    });
  });

  it('should hide contact details on details page if user have no access', async () => {
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: getMockedSeafarerResponseWithNoContactDetailAccess() }),
      );

    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      true,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
    );

    await waitFor(() => {
      const contactDetailElement = renderedView.queryByText('Contact Details');
      expect(contactDetailElement).not.toBeInTheDocument();

      const adress = renderedView.queryByText('Address of Next of Kin');
      expect(adress).not.toBeInTheDocument();

      const telephone = renderedView.queryByText('Telephone No.');
      expect(telephone).not.toBeInTheDocument();

      const mobile = renderedView.queryByText('Mobile No.');
      expect(mobile).not.toBeInTheDocument();

      const email = renderedView.queryByText('Email Address');
      expect(email).not.toBeInTheDocument();
    });
  });

  it('should call route /seafarer/add/basic on click of Edit button when user is on general tab', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1');
    const renderedView = renderDetailsPage(
      true,
      false,
      '/seafarer/details/1',
      '/seafarer/details/:seafarerId',
      false,
      true,
      localHistory,
      true,
    );

    let editButton: HTMLElement;
    await waitFor(() => {
      editButton = renderedView.getByText('Edit');
    });

    await act(async () => {
      fireEvent.click(editButton);
    });
    await waitFor(() => {
      expect(localHistory.location.pathname).toEqual('/seafarer/1/add/basic');
    });
  });

  it('should call route /seafarer/add/experience on click of Edit button when user is on experience tab of detail page', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1/experience');
    const renderedView = renderDetailsPage(
      true,
      false,
      '/seafarer/details/1/experience',
      '/seafarer/details/:seafarerId/:step?',
      false,
      true,
      localHistory,
      true,
    );

    let editButton: HTMLElement;
    await waitFor(() => {
      editButton = renderedView.getByText('Edit');
    });

    await act(async () => {
      fireEvent.click(editButton);
    });

    await waitFor(() => {
      expect(localHistory.location.pathname).toEqual('/seafarer/1/add/experience');
    });
  });

  it('should call route /seafarer/add/bank-accounts on click of Edit button when user is on bank Account tab of detail page', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/details/1/account-details');
    const renderedView = renderDetailsPage(
      true,
      true,
      '/seafarer/details/1/account-details',
      '/seafarer/details/:seafarerId/:step?',
      false,
      true,
      localHistory,
      true,
    );

    let editButton: HTMLElement;
    await waitFor(() => {
      editButton = renderedView.getByText('Edit Bank Accounts');
      expect(editButton).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(editButton);
    });

    await waitFor(() => {
      expect(localHistory.location.pathname).toEqual('/seafarer/details/1/edit-bank-accounts');
    });
  });

  const clickOnActionButton = async (renderedView) => {
    let actionButton: HTMLElement;
    await waitFor(() => {
      actionButton = renderedView.getByText('Actions');
      expect(actionButton).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(actionButton);
    });
  };

  describe('Mark Duplicate HKID Button', () => {
    it('should have Mark Duplicate HKID option in button toolbar', async () => {
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );

      await clickOnActionButton(renderedView);
      await waitFor(() => {
        renderedView.findByText('Mark Duplicate HKID');
        const sh = renderedView.getByTestId('fml-screening-history');
        expect(sh).toHaveTextContent('Screening History');

        const dup = renderedView.getByTestId('fml-mark-duplicate-hkid');
        expect(dup).toHaveTextContent('Mark Duplicate HKID');
      });
    });

    it('should show Mark as Duplicate popup when Mark Duplicate HKID option is clicked ', async () => {
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );
      await clickOnActionButton(renderedView);

      let markDuplicateHKIDBtn: HTMLElement;
      await waitFor(() => {
        renderedView.findByText('Mark Duplicate HKID');
        markDuplicateHKIDBtn = renderedView.getByText('Mark Duplicate HKID');
        expect(markDuplicateHKIDBtn).toBeInTheDocument();
      });
      await act(async () => {
        fireEvent.click(markDuplicateHKIDBtn);
      });

      await waitFor(() => {
        const markAsDuplicateHeader = renderedView.getByText('Mark as Duplicate');
        expect(markAsDuplicateHeader).toBeInTheDocument();
      });
    });

    it('should error popup when clicking on Mark Duplicate HKID of a parent seafarer', async () => {
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: getMockedParentSeafarer() }));
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );

      await clickOnActionButton(renderedView);

      let markDuplicateHKIDBtn: HTMLElement;
      await waitFor(() => {
        renderedView.findByText('Mark Duplicate HKID');
        markDuplicateHKIDBtn = renderedView.getByText('Mark Duplicate HKID');
        expect(markDuplicateHKIDBtn).toBeInTheDocument();
      });
      await act(async () => {
        fireEvent.click(markDuplicateHKIDBtn);
      });
      await waitFor(() => {
        const markAsDuplicateHeader = renderedView.getByText(
          'Unable to mark this Main Profile as other page’s duplicate Secondary Profile',
        );
        expect(markAsDuplicateHeader).toBeInTheDocument();
      });
    });

    it('should show the created by label', async () => {
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: getMockedParentSeafarer() }));
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );

      await clickOnActionButton(renderedView);
      await waitFor(() => {
        const createdBylabel = renderedView.getByText(
          '<NAME_EMAIL> on 23 Jul 2020',
        );
        expect(createdBylabel).toBeInTheDocument();
      });
    });

    it('should show parent profile HKID', async () => {
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: getMockedChildSeafarer() }));
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );

      await clickOnActionButton(renderedView);
      await waitFor(() => {
        const parentProfile = renderedView.getByText('Main Profile HKID (Seafarer ID)');
        expect(parentProfile).toBeInTheDocument();

        const parentProfileHKID = within(parentProfile).getByText('1234');
        expect(parentProfileHKID).toBeInTheDocument();
      });
    });

    it('should show child profile HKID', async () => {
      seafarerService.getChildHKID = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: [{ hkid: 1234 }] }));
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );

      await clickOnActionButton(renderedView);
      await waitFor(() => {
        const childProfile = renderedView.getByText('Duplicate Secondary Profile HKID(s)');
        expect(childProfile).toBeInTheDocument();

        const childProfileHKID = within(childProfile).getByText('1234');
        expect(childProfileHKID).toBeInTheDocument();
      });
    });
  });

  describe('Role based access', () => {
    let renderedView;
    beforeAll(async () => {
      renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1',
        '/seafarer/details/:seafarerId',
        false,
        false,
      );
      await clickOnActionButton(renderedView);
    });
    it('should not have Edit button when user does not have edit role', async () => {
      await waitFor(() => {
        const editBtn = screen.queryByText('Edit');

        expect(editBtn).toBeNull();
      });
    });
    it('should not have Screening Details button when user does not have screening view role', async () => {
      await waitFor(() => {
        const editBtn = screen.queryByText('Screening Details');

        expect(editBtn).toBeNull();
      });
    });
    it('should not have Screening History button in toolbar when user does not have screening view role', async () => {
      await waitFor(() => {
        const screeningHistoryBtn = screen.queryByTestId('fml-screening-history');
        expect(screeningHistoryBtn).not.toBeInTheDocument();
      });
    });
    it('should not have PARIS 2.0 User Account button when user does not have user manage role', async () => {
      await waitFor(() => {
        const screeningHistoryBtn = screen.queryByText('PARIS 2.0 User Account');
        expect(screeningHistoryBtn).not.toBeInTheDocument();
      });
    });
  });

  describe('seafarer experience', () => {
    it('should route to /seafarer/details/:id/experience', async () => {
      seafarerService.getSeafarerExperience = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerExperience() }));

      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1/experience');
      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1/experience',
        '/seafarer/details/:seafarerId',
        false,
        false,
        localHistory,
        true,
      );

      const expectedElements = [
        'Experience Overview',
        'Experience Summary by Vessel Type',
        'Experience Summary by Rank',
        'Experience Summary by Vessel Type / Rank',
      ];

      await waitFor(() => {
        expectedElements.forEach((e) =>
          expect(renderedView.getAllByText(e).length).toBeGreaterThanOrEqual(1),
        );
      });
    });
  });

  describe('Tab wise check Edit button visibility', () => {
    it('should show Edit button when user on general tab', async () => {
      renderDetailsPage(
        true,
        false,
        '/seafarer/details/1/general',
        '/seafarer/details/:seafarerId',
      );

      await waitFor(() => {
        const editBtn = screen.getByText('Edit');

        expect(editBtn).toBeInTheDocument();
      });
    });

    it('should not show Edit button when user on status history tab', async () => {
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1/status-history');
      const renderedView = renderDetailsPage(
        true,
        false,
        '/seafarer/details/1/status-history',
        '/seafarer/details/:seafarerId/:step?',
        false,
        true,
        localHistory,
        true,
      );

      await waitFor(() => {
        const editBtn = renderedView.queryByText('Edit');
        expect(editBtn).not.toBeInTheDocument();
      });
    });
  });

  describe('should show 403 page:', () => {
    it('should show 403 page if seafarer GET api return 403 response', async () => {
      seafarerService.getSeafarer = jest.fn().mockImplementation(() => {
        throw Object.assign(new Error('Error Message'), { response: { status: 403 } });
      });

      const renderedView = renderDetailsPage(
        false,
        false,
        '/seafarer/details/1/general',
        '/seafarer/details/:seafarerId/:step?',
        false,
        true,
      );

      await waitFor(() => {
        const textElement = renderedView.getByText('403');
        expect(textElement).toBeInTheDocument();
      });
    });
  });

  describe('Status Header', () => {
    it('should Update status header to show the signed_on vessel and crewlist', async () => {
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: getSeafarerMockResponse }));
      seafarerService.getSeafarerStatus = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ status: 200, data: getSeafarerStatusMockResponse }),
        );

      const localHistory = createMemoryHistory();
      const renderedView = renderDetailsPage(
        true,
        true,
        '/seafarer/details/10828/general',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );
      await waitFor(() => {
        const vesselName = renderedView.getByText('TRF Miami');
        expect(vesselName).toBeInTheDocument();

        const crewList = renderedView.getByText('Crew List');
        expect(crewList).toBeInTheDocument();
      });
    });

    it('should route to vessel detail page on clicking vessel name', async () => {
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: getSeafarerMockResponse }));
      seafarerService.getSeafarerStatus = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ status: 200, data: getSeafarerStatusMockResponse }),
        );

      const localHistory = createMemoryHistory();
      const renderedView = renderDetailsPage(
        true,
        true,
        '/seafarer/details/10828/general',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );
      let vesselName: HTMLElement;
      await waitFor(() => {
        vesselName = renderedView.getByText('TRF Miami');
        expect(vesselName).toBeInTheDocument();
      });

      await act(async () => {
        fireEvent.click(vesselName);
      });

      await waitFor(() => {
        expect(localHistory.location.pathname).toEqual('/vessel/ownership/details/103');
      });
    });

    it('should route to crew list page on clicking Crew List', async () => {
      const windowOpenMock = jest.fn();
      window.open = windowOpenMock;
      const date = dateAsString(new Date());
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: getSeafarerMockResponse }));
      seafarerService.getSeafarerStatus = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ status: 200, data: getSeafarerStatusMockResponse }),
        );

      const localHistory = createMemoryHistory();
      const renderedView = renderDetailsPage(
        true,
        true,
        '/seafarer/details/10828/general',
        '/seafarer/details/:seafarerId',
        true,
        true,
        localHistory,
      );

      let crewList: HTMLElement;
      await waitFor(() => {
        crewList = renderedView.getByText('Crew List');
        expect(crewList).toBeInTheDocument();
      });

      act(() => {
        fireEvent.click(crewList);
      });
      await waitFor(() => {
        expect(windowOpenMock).toHaveBeenCalledWith(
          `/seafarer/crew-list/vessel/206/${dateAsDash(date)}`,
          '_blank',
        );
      });
    });
  });

  describe.skip('Testing recommendation module', () => {
    it('should recommendation open on button click', async () => {
      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1/experience');
      const renderedView = renderDetailsPage(
        true,
        false,
        '/seafarer/details/1/experience',
        '/seafarer/details/:seafarerId',
        false,
        false,
        localHistory,
        true,
        true,
      );
      await waitFor(async () => {
        const recommendButton = renderedView.getByTestId('recommendation-button');
        expect(recommendButton).toBeTruthy();
        await act(async () => {
          fireEvent.click(recommendButton);
        });
      });

      await waitFor(() => {
        expect(localHistory.location.pathname).toEqual('/seafarer/1/recommendation');
      });
    });
    it('should on Recommend button click open Unable to Recommend model and should include incomplete training requirement error', async () => {
      seafarerService.getSeafarerStatusHistoryByPersonID = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: {} }));

      seafarerService.getRecommendedChecks = jest.fn().mockImplementation(() =>
        Promise.resolve({
          data: mockRecommendationCheckData.recommendedChecksIncompleteTrainingReqData,
        }),
      );

      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1/general');
      renderDetailsPage(
        true,
        false,
        '/seafarer/details/1/general',
        '/seafarer/details/:seafarerId',
        false,
        false,
        localHistory,
        false,
        true,
      );
      await seafarerService.getRecommendedChecks();
      await waitFor(() => {
        const recommendButton = screen.getByTestId('recommendation-button');
        expect(recommendButton).toBeTruthy();
        fireEvent.click(recommendButton);
      });
      await waitFor(() => {
        const unableToRecommendModal = screen.getByText('Unable to Recommend');
        expect(unableToRecommendModal).toBeTruthy();
        expect(seafarerService.getRecommendedChecks).toHaveBeenCalled();
        expect(RECOMMENDATION_INCOMPLETE_TRAINING_REQ).toBeInTheDocument();
      });
    });
    it('should show error message if seafarer have incomplete investigation training', async () => {
      seafarerService.getSeafarerStatusHistoryByPersonID = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: {} }));

      seafarerService.getRecommendedChecks = jest.fn().mockImplementation(() =>
        Promise.resolve({
          data: mockRecommendationCheckData.recommendedChecksIncompleteInvestigationTrainingReqData,
        }),
      );

      const localHistory = createMemoryHistory();
      localHistory.push('/seafarer/details/1/general');
      renderDetailsPage(
        true,
        false,
        '/seafarer/details/1/general',
        '/seafarer/details/:seafarerId',
        false,
        false,
        localHistory,
        false,
        true,
      );
      await seafarerService.getRecommendedChecks();
      await waitFor(() => {
        const recommendButton = screen.getByTestId('recommendation-button');
        expect(recommendButton).toBeTruthy();
        fireEvent.click(recommendButton);
      });
      await waitFor(() => {
        const unableToRecommendModal = screen.getByText('Unable to Recommend');
        expect(unableToRecommendModal).toBeTruthy();
        expect(seafarerService.getRecommendedChecks).toHaveBeenCalled();
        expect(RECOMMENDATION_INVESTIGATION_INCOMPLETE_TRAINING_REQ).toBeInTheDocument();
      });
    });
  });

  describe('createErrorObject', () => {
    it('should create error object format', () => {
      const obj = {
        inner: [
          {
            message: 'Please enter First Name',
            params: {
              path: 'seafarer_person.first_name',
            },
          },
          {
            message: 'Please enter a valid Passport Number',
            params: {
              path: 'seafarer_person.passports[0].number',
            },
          },
          {
            message: 'Please enter Place of Issue',
            params: {
              path: 'seafarer_person.passports[0].place_of_issue',
            },
          },
        ],
      };

      const expectedObj = {
        seafarer_person: {
          first_name: 'Please enter First Name',
          passports: [
            {
              number: 'Please enter a valid Passport Number',
              place_of_issue: 'Please enter Place of Issue',
            },
          ],
        },
      };
      const errorObj = createErrorObject(obj);
      expect(expectedObj).toEqual(errorObj);
    });
  });
}); // NOSONAR
