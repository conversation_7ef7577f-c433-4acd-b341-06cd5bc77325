import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import ContractExpiryTable from '../../component/seafarerList/ContractExpiryTable';

interface OverdueSeafarersListProps {
  seafarers: any[];
  loading: boolean;
  fetchData: ({ pageSize, pageIndex, sortBy }: { pageSize: any; pageIndex: any; sortBy: any; }) => Promise<void>;
  visitSeafarer: (seafarerId: any, activeKey?: string) => void;
  eventTracker: (type: any, value: any) => void;
  selectedColumns: any[];
  quickSearchParams: string;
  advancedSearchParams: any;
  init_sort: any;
  hasMoreData: boolean;
  roleConfig: any;
}

const OverdueSeafarersList: React.FC<OverdueSeafarersListProps> = ({
  seafarers,
  loading,
  fetchData,
  visitSeafarer,
  eventTracker,
  selectedColumns,
  quickSearchParams,
  advancedSearchParams,
  init_sort,
  hasMoreData,
  roleConfig,
}) => {
  return (
    <Container>
      <Row>
        <Col>
          <div className="overdue-seafarers-list">
            {/* Contract Expiry Data is now available here */}
            <div className="mt-3">
              <h4>Overdue Seafarers ({seafarers.length})</h4>
              <p className="text-muted">Contract expiry data is now available in this component.</p>

              {loading && <p>Loading...</p>}
              {!loading && seafarers.length === 0 && <p>No overdue seafarers found.</p>}

              {/* Example: Using the existing ContractExpiryTable component */}
              {!loading && seafarers.length > 0 && selectedColumns.length > 0 && (
                <div>
                  <h5>Option 1: Use Existing ContractExpiryTable Component</h5>
                  <ContractExpiryTable
                    tabName="overdue-seafarers"
                    seafarers={seafarers}
                    fetchData={fetchData}
                    visitSeafarer={visitSeafarer}
                    eventTracker={eventTracker}
                    selectedColumns={selectedColumns}
                    loading={loading}
                    quickSearchParams={quickSearchParams}
                    advancedSearchParams={advancedSearchParams}
                    init_sort={init_sort}
                    hasMoreData={hasMoreData}
                  />
                </div>
              )}

              {/* Example: Show raw data structure for custom implementation */}
              {!loading && seafarers.length > 0 && (
                <div className="mt-4">
                  <h5>Option 2: Raw Data for Custom Implementation</h5>
                  <p>First seafarer data structure:</p>
                  <pre style={{ textAlign: 'left', fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                    {JSON.stringify(seafarers[0], null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default OverdueSeafarersList;
