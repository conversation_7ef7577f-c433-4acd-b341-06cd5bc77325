.nav-border {
    border-bottom: 4px solid #1f4a70;
}

.nav-underline {

    .nav-link {
        color: #797373 !important;
    }

    .active {
        color: #1f4a70 !important;
        font-weight: bold;
    }
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
    background: #1F4A70;
    color: #fff !important;
}

.font-red {
    color: #D41B56;
}

.font-yellow {
    color: #FFC107;
}

.font-green {
    color: #28A747;
}

.font-orange {
    color: #FF8761;
}

.font-pink {
    color: #BD10E0;
}

.font-bold {
    font-weight: bold;
}

.font-black {
    color: #343A40;
}

.switch-advance-search {
    width: 133px;
}

.font-blue {
    color: #1F4A70;
}

.avatar {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: #3c9ef51a;
    color: #1f4a70;
    font-size: 17px;

    &-sm {
        width: 45px;
        height: 45px;
    }
}

#rejected-seafarer-warning .tooltip-inner {
    max-width: 300px;
}

.custom-checkbox {
    input[type=checkbox] {
        accent-color: #0091b8;
        margin-top: 0 !important;
        margin-right: 8px;
        width: 18px;
        height: 18px;
    }
}

.modal-form-title {
    margin-top: 5px;
    margin-bottom: 5px;
}

.modal-header-crew {
    border-bottom: 1px solid #EFEFEF !important;
    font-weight: 500 !important;
}

.modal-crew {

    .btn-secondary.disabled,
    .btn-secondary:disabled {
        background-color: #EFEFEF;
        color: #AAAAAA;
        border-color: #EFEFEF;
    }

    .alert-secondary {
        color: #0091B8;
        background-color: #0091B80D;
        border-color: #0091B84D;
    }

    .modal-body,
    .modal-footer,
    .btn,
    .form-control,
    .dropdown-menu {
        font-size: 14px;
    }

    .form-heading {
        font-weight: 500;
        font-size: 14px;
    }

}

.li-item {
    padding: 3px 5px;
    font-weight: normal;
    font-size: 14px;
    cursor: pointer;
}

.popover .arrow {
    display: none;
}

.pagination-bar {
    padding-left: 0px !important;
    padding-top: 10px !important;
}

.crew-planner {
    .alert-danger {
        color: #D41B56;
    }

    .oval-total-count {
        border-radius: 12px;
        margin-left: 10px;
        padding: 0 10px;
        background: #0091B8;
    }

    .form-control {
        font-size: 14px;
    }

    .react-datepicker-popper {
        z-index: 100;
    }

    .react-datepicker-wrapper {
        font-size: 14px;
    }

    .datepicker-wrapper {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    .datepicker-wrapper .react-datepicker-wrapper {
        width: 100%;
    }

    .datepicker-wrapper .react-datepicker__input-container {
        width: 100%;
    }

    .datepicker-wrapper .icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #6c757d;
        /* You can change this color as needed */
    }


    .input-col {
        flex: 1 1 0;
        /* Flex-grow: 1, flex-shrink: 1, flex-basis: 0 */
        min-width: 100px;
        /* Minimum width for input cols */
        padding: 0 10px;
    }

    .first-col {
        flex: 1.5;
        /* Make the first column take 1.25 times the space */
        min-width: 125px;
        /* Minimum width for the first input col */
    }

}

.oval {
    margin-left: 3px;
    padding: 1px 8px;
    border-radius: 9px;
}

.oval.red {
    background-color: #D41B56;
    color: white;
}

.oval.orange {
    background-color: #FFC107;
    color: #343A40;
}

.oval.blue {
    background-color: #1F4A70;
    color: white;
}

.oval.green {
    background-color: #28a747;
    color: #343A40;
}

.oval.neutral {
    background-color: #EDF3F7;
    color: #343A40;
}

.confirm-recommend-modal {
    .alert-danger {
        color: #D41B56;
    }
}
.spinner-timeline {
    width: 16px;
    height: 16px;
}

.spinner-timeline-p {
    width: 100%;
    display: flex;
    margin-top: 10px;
    margin-left: 30px;
    color: #1F4A70;
    padding: 4px;
    background-color: #e6f2ff;
}

.clubbing-history-table {
    height: 500px;
}

.clubbing-history-title {
    border-bottom: 1px solid #CCCCCC !important;
}
.crew-management-switch-vessel .dropdown-menu {
    top: 100% !important;
}