import * as R from 'ramda';
import moment from 'moment';
import { DateTime } from 'luxon';
import _ from 'lodash';
import XLSX from 'xlsx';
import { toast } from 'react-toastify';
import parsePhoneNumber from 'libphonenumber-js';

export const Dash = '- - -';

export const capitalizeArgs = (...args) => {
  return [...args]
    .filter((s) => s !== Dash)
    .map((s) => capitalizeFirstLetter(s))
    .join(' ')
    .trim();
};

export const capitalizeArgsWithSeparator = (args: any[], separator: string) => {
  return [...args]
    .filter((s) => s) // remove invalid input e.g. undefined, null, empty string
    .filter((s) => s !== Dash)
    .map((s) => capitalizeFirstLetter(s))
    .join(separator);
};

export const decimalString = (value, precision = 2) => {
  if (_.isNumber(value)) return value.toFixed(precision).toString();
  if (_.isString(value) && _.isNumber(parseFloat(value)))
    return parseFloat(value).toFixed(precision).toString();
  return value;
};

export function capitalizeFirstLetter(string) {
  if (typeof string === 'string') return string.charAt(0).toUpperCase() + string.slice(1);
  return '';
}

export function valueOrDash(vessel, children, isCapitalize = false) {
  const data = R.pathOr(Dash, children);
  const value = data(vessel);
  if (isCapitalize && typeof value === 'string' && value !== Dash) {
    return capitalizeFirstLetter(value);
  }
  return value;
}

const parseDurationUnit = (value: any, unit: string, trailingSpace = true) => {
  if (!value) {
    return '';
  }
  if (value == 1) {
    return `${value} ${unit}${trailingSpace ? ' ' : ''}`;
  }
  return `${value} ${unit}s${trailingSpace ? ' ' : ''}`;
};

export const getDuration = (timeInMilliSec) => {
  let day = 1000 * 60 * 60 * 24;

  let days = Math.floor(timeInMilliSec / day);
  let months = Math.floor(days / 31);
  let years = Math.floor(months / 12);

  days -= months * 31;
  months -= years * 12;

  let message = '';
  message += parseDurationUnit(years, 'Year');
  message += parseDurationUnit(months, 'Month');
  message += parseDurationUnit(days, 'Day', false);
  return message.trim() || '0';
};

const calcDurationFromDateRagne = (fromDateISOString, toDateISOString) => {
  const date1 = DateTime.fromISO(fromDateISOString);
  const date2 = DateTime.fromISO(toDateISOString);

  const diff = date2.diff(date1, ['years', 'months', 'days', 'hours']);
  const durationObject = diff.toObject();
  return {
    ...durationObject,
    days: durationObject['days'] + 1, //add 1 day to cater the assumption that the day end at 23:59
  };
};

export const normalizeDuration = (durationObject) => {
  const monthsInYear = 12;
  const daysInMonth = 30; //this is controversial
  const hoursInDays = 24;

  const normalizedDurationObject = {
    ...durationObject,
    hours: _.get(durationObject, 'hours', 0) % hoursInDays,
    days:
      _.get(durationObject, 'days', 0) + parseInt(_.get(durationObject, 'hours', 0) / hoursInDays),
  };

  normalizedDurationObject['months'] =
    _.get(durationObject, 'months', 0) + parseInt(normalizedDurationObject['days'] / daysInMonth);
  normalizedDurationObject['days'] = _.get(normalizedDurationObject, 'days', 0) % daysInMonth;

  normalizedDurationObject['years'] =
    _.get(durationObject, 'years', 0) + parseInt(normalizedDurationObject['months'] / monthsInYear);
  normalizedDurationObject['months'] = _.get(normalizedDurationObject, 'months', 0) % monthsInYear;

  return normalizedDurationObject;
};

export const getDurationByDateRanges = (dateRanges) => {
  const durationObjects = dateRanges.map((dateRange) =>
    calcDurationFromDateRagne(dateRange.fromDateISOString, dateRange.toDateISOString),
  );
  const keySet = durationObjects.reduce((_keySet, durationObject) => {
    Object.keys(durationObject).forEach(_keySet.add, _keySet);
    return _keySet;
  }, new Set());

  const summedDurationObject = Array.from(keySet).reduce((aggDuration, key) => {
    let summedValue = durationObjects.reduce(
      (summedValue, durationObj) => summedValue + _.get(durationObj, key, 0),
      0,
    );
    aggDuration[key] = summedValue;
    return aggDuration;
  }, {});

  return normalizeDuration(summedDurationObject);
};

export const getDurationStrByDateRanges = (dateRanges) => {
  const durationObject = getDurationByDateRanges(dateRanges);

  let { years, months, days } = durationObject;

  let durationStr = '';
  durationStr += parseDurationUnit(years, 'Year');
  durationStr += parseDurationUnit(months, 'Month');
  durationStr += parseDurationUnit(days, 'Day', false);
  return durationStr.trim() || '0';
};

export function dateOrDash(vessel, children) {
  const value = valueOrDash(vessel, children);
  return value !== Dash && value !== '' ? moment(value).format('DD MMM YYYY') : Dash;
}

export function saveToExcel(data, filename) {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');

  const wscols = [{ wch: 25 }, { wch: 50 }];
  worksheet['!cols'] = wscols;
  let result = undefined;

  (data ?? []).forEach((item) => {
    result = addInfoToFile(item.data, item.title, result ? result.sheet : worksheet, {
      c: 0,
      r: result ? result.lastCell.r + 2 : 0,
    });
  });

  XLSX.writeFile(wb, filename + '.xlsx');

  function addInfoToFile(data, title, worksheet, initialCell) {
    if (worksheet === undefined || initialCell === undefined) {
      return;
    }

    let c = initialCell.c;
    let r = initialCell.r;

    if (title != undefined) {
      const cell_ref = XLSX.utils.encode_cell(initialCell);
      XLSX.utils.sheet_add_aoa(worksheet, [[title]], { origin: cell_ref });
      r = r + 1;
    }

    data.forEach((item) => {
      let cell_address = { c: c, r: r };
      let cell_ref = XLSX.utils.encode_cell(cell_address);
      XLSX.utils.sheet_add_aoa(worksheet, [[item.label]], { origin: cell_ref });

      cell_address = { c: c + 1, r: r };
      cell_ref = XLSX.utils.encode_cell(cell_address);
      XLSX.utils.sheet_add_aoa(worksheet, [[item.value]], { origin: cell_ref });

      r = r + 1;
    });

    const lastCell = { c: c, r: r };

    return { sheet: worksheet, lastCell: lastCell };
  }
}

export function saveSeafarersToExcel(data) {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');

  let r = 0;
  const wscols = [{ wch: 5 }, { wch: 25 }, { wch: 15 }, { wch: 15 }, { wch: 20 }];
  worksheet['!cols'] = wscols;

  (data ?? []).forEach((item) => {
    item.forEach((value, index) => {
      const cell_address = { c: index, r: r };
      const cell_ref = XLSX.utils.encode_cell(cell_address);
      XLSX.utils.sheet_add_aoa(worksheet, [[value]], { origin: cell_ref });
    });
    r = r + 1;
  });

  XLSX.writeFile(wb, 'Seafarers.xlsx');
}
export const parseDate = (value: string) => {
  let parsedDate = moment(value, moment.ISO_8601, true);

  // parsing with possible formats
  if (!parsedDate.isValid()) {
    parsedDate = moment(value, 'DD MMM YYYY', true);
  }

  if (!parsedDate.isValid()) {
    console.log('Invalid date or deprecated format detected:', value);
    return null;
  }
  return parsedDate;
};
export function stringAsDate(value: string) {
  if ((typeof value === 'string' || value instanceof String) && value != '') {
    return parseDate(value)?.toDate();
  }
  return undefined;
}

export function dateAsString(date) {
  const str = typeof date === 'string' ? date.trim() : date;
  if (str === null || str === undefined || str === '' || str === Dash || str === 'Invalid Date') {
    return '';
  }
  if (str instanceof Date && Number.isNaN(str.getTime())) {
    return '';
  }

  return parseDate(str)?.format('DD MMM YYYY');
}

export function rankValues(rank_id, rank_data) {
  if (rank_id && rank_data?.length) {
    const rankObj = _.find(rank_data, (rank) => rank_id === rank.id);
    return rankObj?.value;
  }
  return '';
}

export function isDateValid(date) {
  return moment(date).isValid();
}

export function dateAsDayAndTime(date) {
  if (date === null || date === undefined || date === '') return '';
  return moment(date).format('DD MMM YYYY kk:mm');
}

export function covertDaysToYear(days) {
  if (days === null || days === undefined || days === '') return '0';
  return (days / 365).toFixed(2);
}

export function convertYearsToDays(years) {
  if (years === null || years === undefined || years === '') return 0;
  return (parseInt(years) * 365);
}

export function convertDaysToMonthAndDay(days) {
  if (days === null || days === undefined || days === '') return '0';
  const months = Math.floor(days / 30);
  const day = days % 30;
  return `${months}mon ${day}d`;
}

export function dateAsDash(date) {
  if (date === null || date === undefined || date === '') return '';
  return parseDate(date)?.format('YYYY-MM-DD');
}

export const onHandleError = (message, setLoading) => {
  toast.error(`Error: ${message}`);
  setLoading(false);
};

export function toRomanNumber(number) {
  const roman = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
  const decimal = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1]
  let result = '';
  for (let i = 0; i < roman.length; i++) {
    while (number >= decimal[i]) {
      result += roman[i];
      number -= decimal[i];
    }
  }
  return result;
}
export function formatCurrency(value) {
  if (!value) return '0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
}
export const customSortDateFields = (row1, row2, key) => {
  if (row1[key] && row2[key]) {
    const date1 = row1[key] ? moment(row1[key]).valueOf() : '';
    const date2 = row2[key] ? moment(row2[key]).valueOf() : '';
    if (date1 < date2) return -1;
    if (date1 > date2) return 1;
  }
  return 0;
};

export const getAddress = (addressObj, separator = ' ') => {
  if (Object.keys(addressObj).length === 0) return Dash;

  return (
    capitalizeArgsWithSeparator(
      [
        addressObj.address4,
        addressObj.address3,
        addressObj.address2,
        addressObj.address1,
        addressObj.postal_zip_code,
        addressObj.country,
      ],
      separator,
    ) || Dash
  );
};

export const getPhoneNumber = (value: string | number) => {
  let strVal = value.toString();

  if (!strVal.startsWith('+')) {
    strVal = `+${value}`;
  }

  return parsePhoneNumber(strVal)?.formatInternational();
};
