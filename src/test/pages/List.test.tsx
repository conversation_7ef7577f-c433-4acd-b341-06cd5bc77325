import React from 'react';
import { MemoryRouter, Router } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { act, fireEvent, render, waitFor, within } from '@testing-library/react';
import { AccessProvider } from '@src/component/common/Access';
import List from '../../pages/List';
import seafarerService from '../../service/seafarer-service';
import { updateWrapper } from '../../setupTests';
import { screeningStatus } from '../../model/constants';
import SeafarerTable from '../../component/seafarerList/SeafarerTable';
import dropdowndata from '../resources/drop-down-data.json';
import * as mockResponse from '../resources/document-response';
import * as referenceService from '../../service/reference-service';
import vesselService from '../../service/vessel-service';
import { vesselTypeAndMiscDropdown } from '../resources/vessel-experience';
import {
  getShipPartyDataResponse,
  getTechGroupDropDownResponse,
  queryVesselOwnershipResponse,
} from '../resources/getMockedDropDownData';
import '@testing-library/jest-dom';
import { getMockedSeafarerResponse } from '../resources/seafarer-mock-data';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('@tanstack/react-virtual', () => ({
  useVirtualizer: () => ({
    getVirtualItems: () => [
      { index: 0, start: 0, end: 38 },
      { index: 1, start: 38, end: 76 },
    ],
    scrollToIndex: jest.fn(),
    getTotalSize: () => 152,
  }),
}));
describe('<List />', () => {
  beforeAll(async () => {
    jest.setTimeout(*********);
  });

  const renderViewFunc = (localHistory, addSeafarer: boolean, general: boolean) =>
    render(
      <Router history={localHistory}>
        <AccessProvider
          config={{
            seafarer: {
              addSeafarer,
              view: {
                general,
              },
            },
          }}
        >
          <List />
        </AccessProvider>
      </Router>,
    );

  beforeEach(async () => {
    const response = {
      results: [getMockedSeafarerResponse()],
      pagination: {
        totalCount: 1,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));
    seafarerService.getSeafarerListUsernames = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));
    seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getSeafarerDocumentDropdown = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getDropDownDataFromVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ ...vesselTypeAndMiscDropdown }));
    seafarerService.getTechGroupDropDown = jest
      .fn()
      .mockImplementation(() => Promise.resolve(getTechGroupDropDownResponse()));
    vesselService.queryVesselOwnership = jest
      .fn()
      .mockImplementation(() => Promise.resolve(queryVesselOwnershipResponse()));
    seafarerService.getSeafarerReportingOfficeDropDownData = jest
      .fn()
      .mockImplementation(() => dropdowndata.offices);
    jest
      .spyOn(referenceService, 'getVisaRegionDropDownData')
      .mockImplementation(() => Promise.resolve(mockResponse.visaRegionReferenceApiResponseData));
    seafarerService.getShipPartyOwnerList = jest
      .fn()
      .mockImplementation(() => Promise.resolve(getShipPartyDataResponse()));
    vesselService.queryVesselOwnership = jest.fn().mockImplementation(() =>
      Promise.resolve([
        {
          id: 2277,
          name: 'TS Tianjin Copy1',
          status: 'active',
          vessel_id: 1963,
          owner: {
            id: 275,
            value: 'new owner',
          },
        },
      ]),
    );
  });

  describe('should render tab headers', () => {
    it('with default headers', async () => {
      const localHistory = createMemoryHistory();
      const list = renderViewFunc(localHistory, true, true);

      const expectedHeader = [
        'No.',
        'PARIS 1.0',
        'Quality',
        'HKID',
        'Name',
        'Rank',
        'Main Profile HKID',
        'Nationality',
        'Reporting Office',
        'Date of Birth',
        'Availability Date',
        'Availability Remark',
        'Journey Status',
        'Actions',
      ];

      await waitFor(() => {
        expectedHeader.forEach((e) => {
          const header = list.getAllByText(e);
          expect(header[0]).toBeInTheDocument();
          if (e === 'Actions') {
            expect(header[0]).toHaveRole('generic');
          } else {
            expect(header[0]).toHaveRole('columnheader');
          }
        });
      });
    });
  });

  it('should navigate to tab content on click of tab header Rejected', async () => {
    const tabUrl = '/seafarer/archived';
    const response = {
      results: [getMockedSeafarerResponse(), getMockedSeafarerResponse()],
      pagination: {
        totalCount: 2,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));
    const localHistory = createMemoryHistory();
    localHistory.push(tabUrl);
    const list = renderViewFunc(localHistory, true, true);

    let btn: HTMLElement;
    await waitFor(() => {
      btn = list.getByText('Screening Rejected');
      expect(btn).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(btn);
    });
    await waitFor(() => {
      expect(localHistory.location.pathname).toBe('/seafarer/rejected');
    });
  });

  it('should navigate to seafarer details page on click of hkid link', async () => {
    const tabUrl = '/seafarer/passed';
    const response = {
      results: [{ ...getMockedSeafarerResponse(), parent_hkid: 1, id: 1 }],
      pagination: {
        totalCount: 1,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));

    const localHistory = createMemoryHistory();
    localHistory.push(tabUrl);
    const list = renderViewFunc(localHistory, true, true);

    let btn: HTMLElement;
    await waitFor(() => {
      btn = list.getByTestId('fml-seafarer-table-sr-no-0');
      expect(btn).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(btn);
    });
    await waitFor(() => {
      expect(localHistory.location.pathname).toBe('/seafarer/details/1/general');
    });
  });

  it('should render spinner, when seafarers list is on fetch', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/passed');
    const list = renderViewFunc(localHistory, true, true);

    await waitFor(() => {
      const btn = list.getByText('Loading...');
      expect(btn).toBeInTheDocument();
    });
  });

  it('should call route /seafarer/details/:id on click of particular seafarer', async () => {
    const localHistory = createMemoryHistory();
    localHistory.push('/seafarer/passed');
    const list = renderViewFunc(localHistory, true, true);

    let btn: HTMLElement;
    await waitFor(() => {
      btn = list.getByTestId('fml-seafarer-table-sr-no-0');
      expect(btn).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(btn);
    });
    await waitFor(() => {
      expect(localHistory.location.pathname).toContain('/seafarer/details/');
    });
  });

  it('should call route /seafarer/add/basic/ on click of Add a seafarer ', async () => {
    const localHistory = createMemoryHistory();
    const list = renderViewFunc(localHistory, true, true);

    let moreBtn: HTMLElement;
    await waitFor(() => {
      moreBtn = list.getByText('More');
      expect(moreBtn).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(moreBtn);
    });

    let addBtn: HTMLElement;
    await waitFor(() => {
      addBtn = list.getByText('Add a Seafarer');
      expect(addBtn).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(addBtn);
    });
    await waitFor(() => {
      expect(localHistory.location.pathname).toBe('/seafarer/add/basic/');
    });
  });

  it('Should load spinner positioning after table header', async () => {
    const list = render(
      <SeafarerTable
        tabName={`${screeningStatus.PASSED}`}
        seafarers={[]}
        fetchData={() => {}}
        selectedColumns={[]}
        loading
        quickSearchParams={{}}
        advancedSearchParams=""
        init_sort={[]}
        roleConfig={{
          seafarer: {
            addSeafarer: true,
            view: {
              general: true,
            },
          },
        }}
      />,
    );

    await waitFor(() => {
      const table = list.getByTestId('seafarer-table');
      expect(table).toBeInTheDocument();
      const tableChildren = within(table).getByTestId('table-sticky').children;
      expect(tableChildren[0]).toHaveClass('header');
      expect(tableChildren[1]).toHaveClass('body');
      expect(tableChildren[2]).toHaveClass('load-spinner');
    });
  });

  const findSelectedPageNumber = (page) => {
    return Number(page.find('.page-number-border').find('.page-num-active').at(0).text());
  };

  const selectPage = async (page, page_num) => {
    page
      .find('.page-number-border')
      .children()
      .filterWhere((page) => Number(page.text()) == String(page_num))
      .at(0)
      .simulate('click');
    await updateWrapper(page);
  };

  const selectColumn = async (page, col_name) => {
    page
      .find('button')
      .filterWhere((btn) => btn.text() == 'Table Columns')
      .simulate('click');
    await updateWrapper(page);
    /* page
    .find('.dropdown-item')
    .forEach((item) => {console.log("Ss"+item.text()+"SS")}) */
    page
      .find('.dropdown-item')
      .filterWhere((item) => item.text() == col_name)
      .find('a')
      .simulate('click');
    await updateWrapper(page);
  };

  const switchTab = async (page, tabName) => {
    page
      .find('NavLink')
      .filterWhere((link) => link.text() == tabName)
      .simulate('click');
    await updateWrapper(page);
  };

  const selectSize = async (page, size) => {
    page
      .find('.seafarer-table')
      .find('.form-inline')
      .find('select')
      .at(0)
      .simulate('change', { target: { value: Number(size) } });
    await updateWrapper(page);
  };

  const findPageSizeValue = (page) =>
    page.find('.seafarer-table').find('.form-inline').find('select').at(0).props().value;

  const getSelectedColumns = (page) => {
    const header_elements = page.find('.seafarer-table').find('.header').find('.tr').children();
    return header_elements.map((head) => head.text());
  };

  it.skip('should retain page number and page size for each tab on refresh', async () => {
    const data_list = [];
    const default_response = getMockedSeafarerResponse();
    // Replicate mock data 52 times(needed for pagination tests)
    for (let y = 0; y < 52; y++) data_list.push(default_response);
    const data_mock = { results: data_list, pagination: { totalCount: 52 } };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: data_mock }));

    const page = mount(
      <MemoryRouter>
        <List
          roleConfig={{
            seafarer: {
              addSeafarer: true,
              view: {
                general: true,
              },
            },
          }}
        />
      </MemoryRouter>,
    );
    await updateWrapper(page);
    // console.log(page.debug(),"rajivr");
    // Set page size and number in tab 1
    await switchTab(page, 'In Screening');
    await selectSize(page, 20);
    await selectPage(page, 2);
    await selectColumn(page, ' Passport Number');
    // Set a different page size and number in tab 2
    await switchTab(page, 'Screening Rejected');
    await selectSize(page, 10);
    await selectPage(page, 4);
    // Refresh page
    const page_refreshed = mount(
      <MemoryRouter>
        <AccessProvider
          config={{
            seafarer: {
              addSeafarer: true,
              view: {
                general: true,
              },
            },
          }}
        >
          <List />
        </AccessProvider>
      </MemoryRouter>,
    );
    await updateWrapper(page_refreshed);
    // Verify persistence
    await switchTab(page_refreshed, 'In Screening');
    expect(findSelectedPageNumber(page_refreshed)).toEqual(2);
    expect(findPageSizeValue(page_refreshed)).toEqual(20);
    expect(getSelectedColumns(page_refreshed)).toEqual(expect.arrayContaining(['Passport Number']));

    await switchTab(page_refreshed, 'Screening Rejected');
    expect(findSelectedPageNumber(page_refreshed)).toEqual(4);
    expect(findPageSizeValue(page_refreshed)).toEqual(10);
    expect(getSelectedColumns(page_refreshed)).toEqual(expect.arrayContaining(['Passport Number']));
  }, 100000);

  it('should display results based on searched keyword', async () => {
    const tabUrl = '/seafarer/under_screening';
    const response = {
      results: [getMockedSeafarerResponse(), getMockedSeafarerResponse()],
      pagination: {
        totalCount: 2,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));
    const localHistory = createMemoryHistory();
    localHistory.push(tabUrl);
    const list = renderViewFunc(localHistory, true, true);

    let searchBar: HTMLElement;
    await waitFor(() => {
      searchBar = list.getByTestId('search-bar');
      expect(searchBar).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.change(searchBar, { target: { value: 'Hec' } });
    });

    await waitFor(() => {
      const result = list.getAllByText(/Hector/);
      expect(result.length).toBeGreaterThan(0);
    });
  });

  it('should display advanced search overlay component', async () => {
    const tabUrl = '/seafarer/under_screening';
    const response = {
      results: [getMockedSeafarerResponse(), getMockedSeafarerResponse()],
      pagination: {
        totalCount: 2,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));
    const localHistory = createMemoryHistory();
    localHistory.push(tabUrl);
    const list = renderViewFunc(localHistory, true, true);

    let clearBtn: HTMLElement;
    await waitFor(() => {
      clearBtn = list.getByText('Clear All');
      expect(clearBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(clearBtn);
    });

    let searchBtn: HTMLElement;
    await waitFor(() => {
      searchBtn = list.getByTestId('dropdown-advanced-search');
      expect(searchBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(searchBtn);
    });

    let adSearchBtn: HTMLElement;
    await waitFor(() => {
      adSearchBtn = list.getByRole('combobox');
      expect(adSearchBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(adSearchBtn);
    });

    await waitFor(() => {
      const firstName = list.getByText('First Name');
      expect(firstName).toBeInTheDocument();
    });
  });

  it('should display quality checkboxes in advanced search when quality category is selected', async () => {
    const tabUrl = '/seafarer/passed';
    const response = {
      results: [getMockedSeafarerResponse(), getMockedSeafarerResponse()],
      pagination: {
        totalCount: 2,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));

    const localHistory = createMemoryHistory();
    localHistory.push(tabUrl);
    const list = renderViewFunc(localHistory, true, true);

    let clearBtn: HTMLElement;
    await waitFor(() => {
      clearBtn = list.getByText('Clear All');
      expect(clearBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(clearBtn);
    });

    let searchBtn: HTMLElement;
    await waitFor(() => {
      searchBtn = list.getByTestId('dropdown-advanced-search');
      expect(searchBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(searchBtn);
    });

    let adSearchBtn: HTMLElement;
    await waitFor(() => {
      adSearchBtn = list.getByRole('combobox');
      expect(adSearchBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(adSearchBtn);
    });

    let quality: HTMLElement[];
    await waitFor(() => {
      quality = list.getAllByText('Quality');
      expect(quality[0]).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(quality[0]);
    });

    await waitFor(() => {
      expect(list.getByText('Urgent Correction')).toBeInTheDocument();
      expect(list.getByText('No Urgent Correction')).toBeInTheDocument();
      expect(list.getByText('Data is OK')).toBeInTheDocument();
    });
  });

  it('should show reporting office dropdown API is triggered with the user’s office attribute as argument in seafarer advance search when select the reporting office drop down', async () => {
    const tabUrl = '/seafarer/passed';
    const response = {
      results: [getMockedSeafarerResponse(), getMockedSeafarerResponse()],
      pagination: {
        totalCount: 2,
      },
    };
    seafarerService.getSeafarers = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: response }));

    const localHistory = createMemoryHistory();
    localHistory.push(tabUrl);
    const list = renderViewFunc(localHistory, true, true);

    let clearBtn: HTMLElement;
    await waitFor(() => {
      clearBtn = list.getByText('Clear All');
      expect(clearBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(clearBtn);
    });

    let searchBtn: HTMLElement;
    await waitFor(() => {
      searchBtn = list.getByTestId('dropdown-advanced-search');
      expect(searchBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(searchBtn);
    });

    let adSearchBtn: HTMLElement;
    await waitFor(() => {
      adSearchBtn = list.getByRole('combobox');
      expect(adSearchBtn).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(adSearchBtn);
    });

    let reportingOffice: HTMLElement[];
    await waitFor(() => {
      reportingOffice = list.getAllByText('Reporting Office');
      expect(reportingOffice[0]).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(reportingOffice[0]);
    });

    let reportingOfficeDropdown: HTMLElement;
    await waitFor(() => {
      reportingOfficeDropdown = list.getByPlaceholderText('Please select');
      expect(reportingOfficeDropdown).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(reportingOfficeDropdown);
    });
  });
});
