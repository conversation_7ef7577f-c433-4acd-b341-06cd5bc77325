import { groupBy } from 'lodash';
import React, { Fragment } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>u, MenuItem, Typeahead } from 'react-bootstrap-typeahead';
import SearchController from '../../controller/search-controller';

class DropDownSearchMenu extends React.Component {
  render() {
    const props = {};
    props.renderMenu = this._renderMenu;
    props.onChange = this.props.onChange;
    const placeholder = this.props.placeholder ? this.props.placeholder : "Please Select";
    if(this.props.defaultSelected)
      props.defaultSelected = this.props.defaultSelected;
    const controller = new SearchController();
    const searchTypes = controller.getAllFilters(this.props.page).filter((i) => {
      return i.validTabs === undefined || i.validTabs.includes(this.props.tab);
    });


    const handleChange = (event) => {
      props.onChange(event);
    };

    return (
      <Typeahead
        {...props}
        labelKey="name"
        id="search-type-menu"
        onChange={handleChange}
        options={searchTypes}
        placeholder={placeholder}
      />
    );
  }

  _renderMenu = (results, menuProps, state) => {
    let index = 0;
    const sections = groupBy(results, 'section');
    const items = Object.keys(sections).map((section) => (
      <Fragment key={section}>
        {index !== 0 && <Menu.Divider />}
        <Menu.Header>{section}</Menu.Header>
        {sections[section].map((i) => {
          const item = (
            <MenuItem key={index} option={i} position={index}>
              <Highlighter search={state.text}>{i.name}</Highlighter>
            </MenuItem>
          );

          index += 1;
          return item;
        })}
      </Fragment>
    ));

    return <Menu id={menuProps.id}>{items}</Menu>;
  };
}

export default DropDownSearchMenu;
