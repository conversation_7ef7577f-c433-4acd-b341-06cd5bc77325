import moment from 'moment';
import SEARCH_TYPES from './search-types';

const defaultFilters = (key, response = null) => {
  let filters = [];

  if (key === 'contract-expiry') {
    const currentDate = new Date();
    const threeMonthsFutureDate = moment().add(3, 'months').toDate();
    filters = [
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'data_of_contract_expry')[0],
        subtype: {
          startDate: currentDate,
          endDate: threeMonthsFutureDate,
        },
        defaultTab: 'contract-expiry',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'sign_on_ranks')[0],
        subtype: [
          {
            id: 0,
            value: 'All Ranks',
          },
        ],
        defaultTab: 'contract-expiry',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'tech_group')[0],
        subtype: [
          {
            id: 0,
            value: 'All Tech Groups',
          },
        ],
        defaultTab: 'contract-expiry',
      },
      {
        type: SEARCH_TYPES().filter((searchType) => searchType.type === 'offices')[0],
        subtype: [
          {
            id: 0,
            value: 'All Reporting Offices',
          },
        ],
        defaultTab: 'contract-expiry',
      },
      {
        type: SEARCH_TYPES().filter((searchType) => searchType.type === 'vessel')[0],
        subtype: [
          {
            id: 0,
            value: 'All Vessels',
          },
        ],
        defaultTab: 'contract-expiry',
      },
      {
        type: SEARCH_TYPES().filter((searchType) => searchType.type === 'owner')[0],
        subtype: [
          {
            id: 0,
            value: 'All Owners',
          },
        ],
        defaultTab: 'contract-expiry',
      },
      {
        type: SEARCH_TYPES().filter((searchType) => searchType.type === 'nationalities')[0],
        subtype: [
          {
            id: 0,
            value: 'All',
          },
        ],
        defaultTab: 'contract-expiry',
      },
    ];
  }

  if (key === 'available-seafarers') {
    let contractEndDate = moment().subtract(2, 'months').toDate();
    let rank = [
      {
        id: 0,
        value: 'All Ranks',
      },
    ];
    let vesselType = [
      {
        id: 0,
        value: 'All Vessel Types',
      },
    ];
    if (response) {
      rank = response?.rank;
      vesselType = response?.vesselType;
      contractEndDate = response?.contractEndDate;
    }
    filters = [
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'available_date')[0],
        subtype: contractEndDate,
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'account_status')[0],
        subtype: [
          {
            id: 'active',
            value: 'Active',
          },
        ],
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'target_rank')[0],
        subtype: rank,
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'with_fml_vessel_experience')[0],
        subtype: [
          {
            id: 0,
            value: 'All',
          },
        ],
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'target_vessel_type')[0],
        subtype: vesselType,
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'duration_with_company')[0],
        subtype: '0',
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'engine_type')[0],
        subtype: [
          {
            id: 0,
            value: 'All',
          },
        ],
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'duration_in_target_rank')[0],
        subtype: '0',
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'vessel_size')[0],
        subtype: [
          {
            id: 0,
            value: 'All',
          },
        ],
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'duration_on_target_vessel_type')[0],
        subtype: '0',
        defaultTab: 'available-seafarers',
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'offices')[0],
        subtype: [
          {
            id: 0,
            value: 'All',
            ship_part_id: 0,
          },
        ],
        defaultTab: 'available-seafarers',
      },
    ];
  }

  return filters;
};

export default defaultFilters;

export const INITIAL_LOAD_FILTERS = [
  {
    type: SEARCH_TYPES().filter((i) => i.type === 'hkid')[0],
    subtype: '',
  },
];
