import React, { SyntheticEvent, useContext } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Button } from 'react-bootstrap';
import { TabContext } from '../../pages/List';
import { getPageSort } from '../../util/local-storage-helper';

interface Props {
  seafarerId: number;
  eventTracker: Function;
}

const ReplaceButton = ({ seafarerId, eventTracker }: Props) => {
  const history = useHistory();
  const { tab } = useParams();
  const { setActiveKey, setShowAdvSearch, setSignOffLoading, setInitSort } = useContext(TabContext);
  const routeToAvailableSeafarer = (event: SyntheticEvent, seafarerId: number) => {
    event.stopPropagation();
    if (tab && tab === 'overdue-seafarers') {
      setShowAdvSearch(false);
      setActiveKey('available-seafarers');
      setSignOffLoading(true);
      setInitSort(getPageSort('available-seafarers'));
    }
    history.push(`/seafarer/available-seafarers/${seafarerId}`);
  };

  return (
    <Button
      onClick={(e) => {
        eventTracker('replaceSeafarer', 'Routes to Available Seafarers');
        routeToAvailableSeafarer(e, seafarerId);
      }}
      variant="outline-primary"
    >
      Replace
    </Button>
  );
};

export default ReplaceButton;
