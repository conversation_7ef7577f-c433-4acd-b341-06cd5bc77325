body {
  margin-top: 80px;
  background-color: #ffffff;
}

.toolbar-allignment {
  float: right;
  padding-top: 24px;
}

.paris2-icon.default {
  svg {
    color: #1f4a70;
  }
}

// TAKEOVER TAB NAVIGATION
.tab_navigation {
  &__title {
    margin-bottom: 20px;
    color: #aaaaaa;
    font-size: 17px;
    font-weight: 500;
    text-align: center;
    height: 20px;

    &.active {
      color: #1f4a70;
      cursor: pointer;
    }
  }

  &__progress_line {
    background-color: #cccccc;
    height: 4px;
    width: 100%;

    &.active {
      background-color: #1f4a70;
      cursor: pointer;
    }
  }

  &__progress_dot {
    background-color: #cccccc;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    margin-left: 45%;
    margin-top: -12px;

    &.active {
      background-color: #1f4a70;
      cursor: pointer;
    }
  }
}

.takeover_error_list {
  margin-top: 40px;
}

// DETAILS PAGE STYLING
.details_page {
  margin-bottom: 140px;

  .paris2-icon {
    float: left;
    cursor: pointer;

    svg {
      color: #1f4a70;
    }
  }

  .d-flex.flex-row {
    color: #1f4a70;
  }

  .paris2-icon.arrow-left {
    margin: 0px 4px 0 0;

    svg {
      vertical-align: unset;
    }
  }

  h4 {
    margin: 0px;
    padding: 0px;
  }

  .paris1-link-detail-page {
    color: #1f4a70;
    text-decoration: underline !important;
    padding-left: 5px;
  }

  .selected-file-link {
    color: #1f4a70;
    cursor: pointer;
    text-decoration: underline;
  }

  .more-documents-wrapper {
    height: 50px;
    padding-top: 10px;
    padding-left: 10px;
    font-size: 14px;
  }

  .icon__draft__circle {
    position: absolute;
    border-radius: 50%;
    top: 38px;
    opacity: 0.8;
    display: block;
    height: 200px;
    width: 200px;
    right: 0;
    left: 0;
    margin-right: auto;
    margin-left: auto;
    background-color: #1f4a70;
  }

  .paris2-icon.vessel_image_draft {
    position: absolute;
    top: 20%;
    opacity: 0.8;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 50% !important;
    height: 50% !important;

    svg {
      color: white;
    }
  }
  .pre-joing-tab {
    .form-label {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }
  }

  .paris2-icon.vessel_image {
    margin: 0;

    svg {
      color: white;
    }
  }

  &__draft-alert {
    padding: 5px;
    padding-left: 15px;
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 30px;
    height: 44px;

    &.under_screening {
      color: #0091b8;
    }

    &.passed {
      color: #28a747;
    }

    &.rejected {
      color: #d41b56;
    }
  }

  tr:hover td {
    background: #eff8fa;
  }

  &__edited_label {
    font-size: 14px;
    font-weight: 400;
    margin-left: 30px;
    color: #6c757d;
  }

  &__row-name {
    width: 50%;
    font-size: 14px;
    font-weight: 700;
    color: #343a40;

    &__header {
      font-weight: 600;
      color: #1f4a70;
      border-top: none !important;
      border-bottom: 1px solid #efefef;

      &__not-first {
        padding-top: 20px !important;
        color: #1f4a70;
      }
    }
  }

  &__row-value {
    width: 50%;
    font-size: 14px;

    &__header {
      font-weight: 600;
      color: #1f4a70;
      border-top: none !important;
      border-bottom: 1px solid #efefef;

      &__not-first {
        padding-top: 20px !important;
        color: #1f4a70;
      }
    }
  }

  &__table_head {
    border-top: 4px solid #1f4a70;
    color: #1f4a70;
    font-size: 16px;
    font-weight: 500;

    th {
      border: none !important;
    }
  }

  td {
    border-top: none;
    border-bottom: 1px solid #efefef;
  }

  tbody {
    border: none !important;
  }

  &__texted_ship_report {
    margin-top: 30px;
    color: #343a40;
    font-size: 14px;

    &__bold {
      font-weight: 600;
    }
  }
}

.not-to-be-employed-alert {
  background-color: #fbf3f6;
  color: #d41b56;
  font-size: 16px;
  font-weight: 600;

  .reason {
    color: #6c757d;
    font-size: 14px;
    font-weight: 400;
    width: 80%;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    margin-top: 3px;
    vertical-align: top;
  }
}

.ship-report-wrapper {
  color: #edf3f7;
  font-size: 14px;
  font-weight: 400;
  background-color: #1f4a70;
  margin-top: 40px;

  a {
    color: white;
  }

  h4 {
    color: #1f4a70;
  }

  &__first-row {
    border-bottom: 1px solid white;
    padding-left: 10px;
    padding-top: 20px;
    padding-bottom: 25px;
  }

  &__second-row {
    border-bottom: 1px solid white;
    padding-left: 10px;
    padding-top: 20px;
    padding-bottom: 25px;
  }

  &__third-row {
    padding-left: 10px;
    padding-top: 20px;
    padding-bottom: 25px;
  }

  &__fourth-row {
    border-left: 1px solid white;
    padding-left: 20px;
    padding-top: 20px;
    padding-bottom: 25px;
  }

  &__bold {
    width: 100%;
    font-weight: 600;
  }

  &__underline {
    text-decoration: underline;
  }

  &__location {
    margin-bottom: 25px;
  }
}

.carousel-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

hr.dashed_line {
  border-top: 1px dashed #1f4a70;
}

hr.section_line {
  border-top: 2px solid #1f4a70;
}

hr.grey_line {
  border-top: 2px solid #cccccc;
}

hr.dark_grey_line {
  border-top: 2px solid #999999;
}

hr.lg {
  border-width: 4px;
}

.p-0.m-0 {
  a {
    color: inherit;
    text-decoration: none;
    background-color: transparent;
  }
}

.form-label {
  font-size: 14px;
  color: #333333;
}

.pdf-form-label {
  font-size: 12px;
  color: #1f4a70;
}

.pdf-form-value {
  font-size: 12px;
  color: #333333;
}

.table {
  width: 100%;
}

.loading {
  display: block;
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.warning.form-control.is-invalid {
  border-color: #ffa221;
  background-image: none;
}

.warning.invalid-feedback {
  color: #fc592e;
}

.form-control.clear-name {
  border: 1px solid #28a747;
}

.small-spinner {
  padding-top: 36px;

  .spinner-border.text-secondary {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 0.15em;
  }
}

.document-copy-feedback {
  display: block;
}

// List
.search-parameters-wrapper {
  background-color: rgba(0, 145, 184, 0.05);
  margin-top: 20px;
  margin-bottom: 20px;

  &__title {
    color: #1f4a70;
    margin-top: 10px;
    font-size: 14px;
    font-weight: 600;
  }

  &__params {
    color: #1f4a70;
    margin-bottom: 14px;
    font-size: 14px;
    font-weight: 400;
  }

  &__slash {
    font-weight: 600;
    font-size: 16px;
  }

  &__button-wrapper {
    text-align: right;
  }
}

.quick-filters-button {
  color: #1f4a70;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;

  div {
    float: left;
    margin-right: 4px;
  }
}

.hidden {
  display: none;
}

.spinner-container {
  width: 100%;
  height: 200px;
  text-align: center;
  padding-top: 150px;
}

.add_seafarer_page {
  margin-bottom: 120px;

  &__section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f4a70;
  }

  &__subsection-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    display: inline-block;
  }

  &__subsection-title-remove {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    display: inline-block;
  }

  .form-label {
    font-weight: 500;
  }

  .solid-gray-line {
    color: #efefef;
  }
}

.selected-file-wrapper {
  background-color: #f3f9fb;
  border-radius: 4px;
  padding-left: 12px;
  padding-right: 12px;
  margin-left: 12px;
  margin-right: 20px;
  width: 78%;
  display: flex;
  align-items: center;

  .selected-file-link {
    color: #1f4a70;
    cursor: pointer;
    display: inline-block;
    word-break: break-all;
    text-decoration: underline !important;
  }
}

// SCREENING HISTORY
.screening_history {
  padding-top: 20px;

  .paris2-icon {
    cursor: pointer;
    float: right;
    color: #1f4a70;
  }

  .table {
    margin-top: 30px;
    color: #343a40;

    th {
      border-style: none;
      border-top: 3px solid #1f4a70;
    }

    tr {
      height: 58px;
      border-style: none;
      border-top: 1px solid #efefef;

      td {
        vertical-align: middle;
        border-style: none;

        a,
        .btn-link {
          color: #0091b8;
          text-decoration: underline !important;
        }
      }
    }
  }
}

.information-modal {
  .modal-title {
    color: #343a40;
    font-size: 20px;
    font-weight: 600;
  }

  .modal-footer {
    .btn-primary {
      background-color: #0091b8;
      border: none;
    }
  }
}

.duplicate-seafarers {
  margin-top: 30px;
  border-radius: 4px;
  padding: 20px;
  background-color: #faf2f5;

  .title {
    color: #ff5a39;
    font: normal normal bold 16px/20px Inter, sans-serif;
  }

  .subtitle {
    margin-top: 8px;
    color: #333333;
    font: normal normal normal 16px/20px Inter, sans-serif;

    .reason {
      font: normal normal bold 16px/20px Inter, sans-serif;
    }
  }

  .links {
    color: #1f4a70;
    text-decoration: underline;

    div {
      cursor: pointer;
    }
  }
}

// DOCUMENTS
.documents_page {
  .seafarer-header {
    padding-left: 25px;
    padding-right: 25px;
  }

  .paris2-icon {
    cursor: pointer;
    float: right;
    color: #1f4a70;
  }

  .hr-border {
    border-top: 3px solid #1f4a70;
    margin-top: 0px;
  }

  .table-title {
    margin-left: 10px;
    margin-right: 10px;
    color: #1f4a70;
    font-weight: 600;
    font-size: 16px;
  }

  .nav {
    margin-top: 10px;
    margin-bottom: 20px;
  }

  .table {
    color: #343a40;
    margin-bottom: 0px;

    thead {
      border-style: none;

      tr {
        border-style: none;
      }
    }

    th {
      border-style: none;
    }

    tr {
      height: 58px;
      border-style: none;
      border-top: 1px solid #efefef;

      td {
        vertical-align: middle;
        border-style: none;

        a {
          color: #0091b8;
          text-decoration: underline !important;
        }

        .selected-file-link {
          color: #0091b8;
          cursor: pointer;
          text-decoration: underline;
        }
      }
    }
  }

  &__row-column1 {
    font-weight: 600;
  }
}

//Account Details Page
.accdetails_page {
  .paris2-icon {
    cursor: pointer;
    float: right;
    color: #1f4a70;
  }
}

.seafarer-header-row {
  margin-bottom: 20px;
}

//approval status styles
tr.pending>td:nth-child(4) {
  color: orange;
}

tr.approved>td:nth-child(5) {
  color: #28a747;
}

tr.rejected>td:nth-child(4) {
  color: #d41b56;
}

tr.forwarded>td:nth-child(4) {
  color: #1f4a70;
}

tr.reapplied>td:nth-child(4) {
  color: #1f4a70;
}

// VESSEL APPROVAL STYLING
.screening-page {
  h4 {
    margin-top: 20px;
  }

  hr {
    border: 1px solid #1f4a70;
  }

  hr.table-section {
    border: 1px dashed #1f4a70;
  }

  .wco-status {
    .paris2-icon {
      float: left;
      margin-right: 5px;
    }
  }

  .wco-status.success {
    color: #28a747;

    .paris2-icon {
      svg {
        color: #28a747;
      }
    }
  }

  .wco-status.error {
    color: #f00;

    .paris2-icon {
      svg {
        color: #f00;
      }
    }
  }

  .paris2-icon {
    cursor: pointer;
    float: right;
    color: #1f4a70;
  }

  &__section-header {
    margin-bottom: 25px;
    color: #1f4a70;
  }

  &__table-section-header {
    color: #1f4a70;
    font-weight: bold;
  }

  &__table-section-header-textarea {
    color: #1f4a70;
    font-size: 12px;
    margin-bottom: 25px;
  }

  .table {
    table-layout: fixed;
    font-size: 14px;
    color: #343a40;

    .row {
      margin-bottom: 35px;
    }

    tr {
      word-break: break-all;
    }

    th,
    td {
      padding-left: 0px;
    }

    .button-row {
      border-top: none;
      border-bottom: 1px solid #dee2e6;
    }

    tr span {
      display: block;
      margin-bottom: 20px;
    }

    thead th {
      border-top: none;
    }

    tr>th>span:first-child,
    tr>td:nth-child(4) {
      text-transform: capitalize;
    }
  }

  table.in-active {
    opacity: 0.5;
  }

  img.icon__close {
    cursor: pointer;
    float: right;
  }

  &__moved-tick {
    path {
      fill: #28a747;
    }

    margin-right: 10px;
  }

  .btn-link {
    color: #0091b8;
  }
}

// MULTIPLE FILE UPLOAD
.file-upload-area {
  height: 218px;
  background-color: #f3f9fb;
  border-radius: 4px;
  cursor: pointer;

  .select-message-view {
    pointer-events: none;
    color: #1f4a70;
    display: table;
    margin: 0 auto;
    height: 100px;
    width: 380px;
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);

    .title {
      font-size: 16px;
      font-weight: 600;
      padding-bottom: 16px;
      text-align: center;
    }

    .message {
      font-size: 14px;
      font-weight: 400;
      text-align: center;
    }
  }
}

.upload-case-report-label {
  color: #d41b56;
  font-size: 14px;
  font-weight: 400;
  margin-top: 10px;
}

.selected-files-wrapper {
  margin-top: 30px;
  padding-left: 0px;

  .title {
    color: #343a40;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-left: 16px;
  }

  .selected-file-row {
    display: flex;
    margin-bottom: 30px;

    .remove_icon {
      margin-top: 16px;
      margin-left: 12px;
    }

    &__selected-file-wrapper {
      background-color: #f3f9fb;
      border-radius: 4px;
      width: 82%;
      height: 40px;
      margin-top: 10px;
      padding-left: 12px;
      padding-right: 12px;
      align-items: center;
      display: flex;

      .selected-file-link {
        color: #1f4a70;
        cursor: pointer;
        text-decoration: underline !important;
      }
    }
  }
}

.selected-files-label {
  color: #343a40;
  font-size: 14px;
  font-weight: 400;
}

.uploaded-files-wrapper {
  margin-top: 30px;
  padding-left: 0px;

  .title {
    color: #343a40;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .uploaded-file-label {
    color: #343a40;
    font-size: 14px;
    font-weight: 600;
  }

  .uploaded-file-row {
    display: flex;
    margin-bottom: 30px;

    &__uploaded-file-wrapper {
      background-color: #f3f9fb;
      border-radius: 4px;
      width: 82%;
      height: 40px;
      margin-top: 10px;
      padding-left: 12px;
      padding-right: 12px;
      align-items: center;
      display: flex;

      .uploaded-file-link {
        color: #1f4a70;
        cursor: pointer;
        text-decoration: underline !important;
      }
    }
  }
}

// MODAL OVERRIDES
.action-modal {
  & .modal-content {
    span {
      display: block;
    }

    word-wrap: break-word;
  }

  & .modal-dialog {
    width: 460px;
  }
}

.modal-header {
  border-bottom: 0px;
}

.modal-header-border {
  border-bottom: 1px solid #dee2e6 !important;
}

.modal-footer-border {
  border-top: 1px solid #dee2e6 !important;
}

.modal-footer {
  border-top: 0px;
}

@media print {

  .no-print,
  .no-print * {
    display: none !important;
  }
}

// danger alert

.alert-heading {
  font-size: 16px;
  font-weight: 500;
  color: #d41b56;
}

.alert-danger {
  background-color: #faf2f5;
  border: none;
}

.alert-danger li {
  color: #333333;
}

// ADVANCED SEARCH STYLING
.advanced_search {
  button {
    padding-left: 16px;
    padding-right: 16px;
  }

  hr.line2px {
    border-top: 2px solid #1f4a70;
  }

  hr.line1px {
    border-top: 1px solid #efefef;
    padding-bottom: 0px;
    margin-bottom: 20px;
    margin-top: 20px;
    width: calc(100% + 30px);
    margin-left: -15px;
  }

  &__title {
    margin-top: 16px;
    margin-bottom: 24px;
  }

  &__subtitle {
    color: #1f4a70;
    margin-top: 30px;
    margin-bottom: 24px;
  }

  .paris2-icon {
    cursor: pointer;
    float: right;

    svg {
      color: #1f4a70;
    }
  }

  .paris2-icon.remove {
    margin-top: 4px;
    margin-left: 10px;
    float: none;
  }

  .label-to-on-top {
    margin-top: 6px;
    color: #333333;
  }

  .label-to-in-row {
    margin-top: 8px;
    color: #333333;
  }

  .hidden-label {
    color: #fafafa;
  }

  &__input-disabled {
    color: #333333;
    background-color: #efefef !important;
    border: 1px solid #cccccc;
  }

  &__filter-row {
    padding-top: 20px;
    padding-bottom: 4px;
    border-bottom: 1px solid #efefef;
  }

  &__filter-row-borderless {
    padding-bottom: 4px;
  }

  &__clear-button {
    margin-top: 10px;
  }

  &__add-button {
    height: 38px;
    margin-top: 20px;
  }

  &__show-results-button {
    height: 38px;
    margin-top: 40px;
  }

  .category {
    margin-bottom: -15px;
    padding-left: 5px;
  }
}

// DATE PICKER
.react-datepicker-wrapper {
  width: 100%;
  height: 38px;
}

.react-datepicker__input-container {
  width: 100%;
  height: 38px;
}

.react-datepicker__input-container input {
  width: 100%;
  height: 38px !important;
  border-color: #cfd4d9;
  border-radius: 4px;
  border-width: 1px;
  border-style: solid;
  color: #555b60;
  padding-left: 10px;
}

.date-picker-disabled {
  color: #333333;
  background-color: #e9ecef !important;
  border: 1px solid #cccccc;
}

.date-picker-wrapper-disabled button {
  display: none;
}

//SCROLL TO TOP BUTTON
.back-to-top {
  .paris2-icon {
    z-index: 999;
    position: fixed;
    bottom: 2rem;
    right: 5rem;
    animation: fadeIn 1ms ease-in-out 1ms both;
    cursor: pointer;

    svg {
      color: #1e4a70;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  span {
    z-index: 999;
    position: fixed;
    bottom: 0rem;
    right: 4rem;
    animation: fadeIn 1ms ease-in-out 1ms both;
    cursor: pointer;
  }
}

// bottom button component
.bottom-component {
  height: 58px;
  background-color: #f8f9fa;
  border-top: 1px solid #cccccc;
  text-align: center;
  padding-top: 10px;

  &__button {
    padding-left: 100px;
    padding-right: 100px;
  }
}

//FOR INPUT MEASUREMENT APPEND
.unit-of-measure {
  width: 55px;
  justify-content: center;
}

//FOR TAKEOVER HEADER
.closeIcon {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;

  .paris2-icon {
    cursor: pointer;
    float: right;

    svg {
      color: #1f4a70;
    }
  }
}

.remove_icon {
  margin-top: 5px;
  cursor: pointer;
}

.btn:focus {
  box-shadow: none !important;
}

.btn.btn-link {
  color: #1f4a70;
  text-decoration: underline;
  padding: 0 0.4rem;
}

.text-align-left {
  text-align: left;
}

.dropdown-item:hover {
  background-color: #eff8fa;
}

.dropdown-header {
  color: #1e4a70;
  font-weight: 800;
}

.List__PopoverMenu {
  list-style-type: none;
  padding-left: 5px;
  width: 130px;

  li {
    cursor: pointer;
    margin: 10px;
  }

  &__crew-management {
    margin-bottom: 0px;
    width: 180px;

    li {
      cursor: pointer;
      margin: 5px;
      padding-right: 10px;
    }
  }
}

//FOR ADDING ASTERIX
.required {
  input[type='radio']+label:after {
    content: '';
  }

  label:after {
    content: '*';
  }
}
.fixed-horizontal-scrollbar {
  position: sticky;
  bottom: 0;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  background-color: #fff;
  border-top: 1px solid #eee;
  z-index: 10;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-x;
  height: 16px;
}

.seafarer-table {
  .page-number-border {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 0px 8px;
  }

  .top-pagination-bar {
    padding-bottom: 0 !important;
  }

  .hr-1px {
    border: 1px solid #cccccc;
    margin-bottom: 0;
  }

  .page-num-active {
    text-decoration: underline;
  }

  .page-num-inactive {
    text-decoration: initial;
  }

  .page-num-disabled {
    color: #cccccc;
  }

  .page-num-enabled {
    color: #1f4a70;
  }

  .page-num {
    display: inline-block;
    cursor: pointer;
    margin: 8px;
    user-select: none;
  }

  .table {
    border-spacing: 0;
    width: 100%;
    padding-top: 20px;
    min-width: 0 !important;

    .th {
      background-color: #ffffff;
      margin: 0;
      padding: 0.75em 0.7em;
      border-bottom: 2px solid #1f4a70;
      font-weight: bold;
      user-select: none;
    }

    .th:first-child {
      padding-left: 1.5em;
    }

    .body .tr>.td {
      border-bottom: 1px solid #cccccc;
    }

    .body>.tr:hover>.td {
      background-color: #eff8fa;
    }

    .td {
      background-color: #ffffff;
      margin: 0;
      padding: 0.7em;

      a {
        color: #1f4a70;
      }
    }

    .td:first-child {
      padding-left: 1.5em;
    }

    &.sticky {
      overflow-x: auto;

      .footer {
        width: fit-content;
        bottom: 0;
      }

      .header {
        top: 0;
      }

      .body {
        position: relative;
      }

      [data-sticky-last-left-td] {
        border-right: 1px solid #cccccc;
      }
    }

    &.status_history {
      padding-top: 0;

      .th:last-child {
        text-align: left;
      }

      [data-sticky-last-left-td] {
        border-right: none;
        text-align: left;
      }
    }

    &.contract-expiry-list-table {
      overflow-x: auto;

      #top-header-0 {
        .th {
          border-bottom: none;
          border-right: 2px solid #1f4a70;
          border-top: 2px solid #1f4a70;
        }

        .th:first-child {
          border-right: none;
        }

        .th:last-child {
          border-right: none;
          text-align: left;
        }
      }

      #top-header-1 {
        .th:nth-last-of-type(4) {
          border-right: 2px solid #1f4a70;
        }

        .th:nth-last-of-type(7) {
          border-right: 2px solid #1f4a70;
        }

        .th:last-child {
          text-align: left;
        }
      }

      .td:nth-last-of-type(4) {
        border-right: 2px solid #1f4a70;
      }

      .td:nth-last-of-type(7) {
        border-right: 2px solid #1f4a70;
      }
    }

    &.vessel-plan {
      .td:nth-child(2) {
        border-right: 1px solid #cccccc;
      }
    }

    .load-overlay {
      height: 100%;
      width: 100%;
      z-index: 999;
      background-color: rgba(0, 0, 0, 0.2);
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .min-width-auto {
    min-width: auto !important;
  }
}

.appraisal-table .seafarer-table .table {
  .th {
    background-color: #ffffff;
    margin: 0;
    padding: 0.75em 0.7em;
    border-bottom: 2px solid #efefef !important;
    font-weight: bold;
    user-select: none;
  }

  .body .tr>.td {
    border-bottom: 1px solid #efefef !important;
  }

  .td:first-child {
    padding-left: 0.7em;
  }
}

.load-spinner {
  margin: 5% 0%;
  justify-content: center;

  div {
    color: #1f4a70 !important;
  }
}

//to set display to block
.set-display-block {
  display: block;
}

.search-text-highlight {
  background: #add0e4 0% 0% no-repeat padding-box;
}

.advanced-search-seafarer {
  .advanced-search-seafarer-menu {
    width: 97vw;
    padding: 1rem;
  }

  .dropdown-menu {
    &[aria-labelledby='dropdown-advanced-search'] {
      margin-left: -1vw !important;
      border: none;
      background: #f8f9fa 0% 0% no-repeat padding-box;
      margin-top: 10px;
    }
  }

  .add_btn {
    background-color: #ffffff;
    color: #1f4a70;

    &:hover {
      background-color: #1f4a70;
      color: #ffffff;
    }
  }

  .advanced_search_header {
    font: normal normal bold 14px/17px Inter, sans-serif;
    color: #1e4a70;
  }
}

.mobile {
  @media screen and (min-width: 577px) {
    display: none;
  }

  @media screen and (max-width: 576px) {
    display: block;
  }
}

.mobileAdvSearch {
  @media screen and (min-width: 961px) {
    display: none;
  }

  @media screen and (max-width: 960px) {
    display: block;
  }
}

.desktopAdvSearch {
  @media screen and (min-width: 961px) {
    display: block;
  }

  @media screen and (max-width: 960px) {
    display: none;
  }
}

.seafarer-list-count {
  margin-left: auto;
  margin-right: 25px;
  margin-top: 25px;
}

.desktop {
  @media screen and (min-width: 577px) {
    display: block;
  }

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.subtype-field {
  padding: 0 !important;

  .spantoken {
    position: absolute;
    display: contents;
  }

  .multi-dropdown {
    .dropdown-menu {
      .dropdown-item {
        padding-left: 0.5rem;
      }
    }

    .check-icon {
      display: flex;

      svg {
        color: #0091b8;

        path {
          fill: #0091b8;
        }
      }
    }

    .empty-icon-padding {
      padding-left: 30px;
    }
  }

  .rbt-input-multi.form-control {
    .rbt-input-wrapper {
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 1.5rem;
      overflow: hidden;
      position: relative;

      div {
        position: absolute !important;
      }

      input.rbt-input-main.custom-input {
        background-color: transparent;
        white-space: nowrap;
        border: 0px;
        box-shadow: none;
        cursor: inherit;
        outline: none;
        padding: 0px;
        width: 100%;
      }
    }

    input {
      position: absolute;
      top: 0;
    }
  }
}

.token {
  background-color: #e7f4ff;
  border: 0;
  border-radius: 0.25rem;
  color: #007bff;
  display: inline-block;
  margin: 1px 3px 2px 0;
  padding: 0px 7px;
  position: relative;

  button {
    padding: 0;
    padding-left: 3px;
  }
}

.red {
  background-color: #d41b56;
}

.orange {
  background-color: #ffa221;
}

.green {
  background-color: #28a747;
}

.parent-hkid-label {
  font-weight: 500;
}

.mark-duplicate-savebtn {
  min-width: 78px;

  &:disabled {
    background: #cccccc 0% 0% no-repeat padding-box;
    border-radius: 4px;
    border-color: #cccccc;
  }
}

.duplicate_alert {
  &.heading {
    font: normal normal bold 16px/20px Inter, sans-serif;
    color: #d41b56;
  }

  &.content {
    color: #333333;
  }

  .parent-link {
    vertical-align: baseline;
    font-size: 1rem;
    padding-left: 0;
  }
}

.parent-seafarer-error {
  font: normal normal normal 14px/17px Inter, sans-serif;
  letter-spacing: -0.77px;
  color: #d41b56;

  &.parent-link {
    vertical-align: baseline;
    font-size: 14px;
    padding-left: 0;
  }
}

.parent-seafarer-name {
  font: normal normal normal 14px/17px Inter, sans-serif;
  letter-spacing: -0.77px;
  color: #1f4a70;
}

.btn-toolbar {
  .dropdown-item {
    text-decoration: none !important;
  }
}

.button-link {
  color: #0091b8;
  text-decoration: underline !important;
  padding: 0;
}

.quality-tooltip>div.tooltip-inner {
  background-color: #1f4a70;
  color: white !important;
  box-shadow: 0px 3px 6px #00000029;
}

.quality-tooltip.show {
  opacity: 1 !important;
}

.quality-tooltip>div.arrow::before {
  border-bottom-color: #1f4a70;
  color: white !important;
}

.duplicate_child.content {
  margin-left: 5px;
}

.confirm-btn {
  &:disabled {
    background: #cccccc 0% 0% no-repeat padding-box;
    border-radius: 4px;
    border-color: #cccccc;
  }
}

.no-result-found {
  text-align: center;
}

.alert-icon-no-search {
  width: 45px;
  margin: 10px;
}

.checkbox-field {
  display: flex;
  align-items: center;

  .form-check-input {
    width: 18px;
    height: 18px;
    margin-top: 0px;
  }

  .form-check-label {
    font-size: 14px;

    .dot {
      height: 8px;
      width: 8px;
      display: inline-block;
      margin-left: 4px;
      margin-right: 4px;
      border-radius: 50%;
    }
  }
}

.big-checkbox {
  display: flex;
  align-items: center;
  text-indent: 1rem;

  .form-check-input {
    width: 20px;
    height: 20px;
    margin-top: 0px;
  }
}

.bread-crump-wrapper {
  display: inline-block !important;

  ol {
    background-color: #fff !important;
    margin-bottom: 0px;
    padding: 0px 5px;
  }
}

.breadcrumb-text {
  font: normal normal normal 24px/29px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70 !important;
}

.inactive-breadcrumb-text a {
  @extend .breadcrumb-text;
  text-decoration: underline !important;
}

.breadcrumb-item+.breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #1f4a70 !important;
  content: '/';
}

.flex-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.is-original {
  color: #212529 !important;
}

.no-records {
  color: #aaaaaa;
  border-bottom: 1px solid #efefef !important;

  .alert-icon svg {
    color: #aaaaaa;
    width: 20px;
    margin: 10px;
  }
}

.document-table-title {
  color: #1f4a70;
}

.is-national {
  font-size: 0.85rem;
  font-weight: 600;
  color: black;
  padding-top: 5px;
}

.document-table-is-national-row {
  font-weight: 600;
}

.table-sub-heading {
  width: 100%;
  background: #f8f9fa 0% 0% no-repeat padding-box;
  opacity: 1;
  color: #1f4a70;
  margin-top: 10px;
  padding: 7px;
  padding-left: 3%;
}

.create-error {
  color: #d41b56;
  margin-left: 1rem;
  margin-bottom: 1rem;
}

.spinner-wrapper {
  position: absolute;
  width: 100%;
  text-align: center;
  padding-top: 10px;
}

.no-records-text-align {
  position: absolute;
  width: 97%;
  height: 40px;
  padding: 0px !important;
}

.action-column {
  display: flex;
  justify-content: center;
  align-items: center;
}

.required-field-text {
  font-size: 16px;
  color: #333333;
}

.dropdown-option-group {
  color: #aaaaaa;
}

.dropdown-option {
  color: #1e4a70;
}

.vessel-form-label {
  border-top: 1px solid #cccccc;
  padding-bottom: 10px;
  padding-top: 5px;
  font-size: 16px;
  color: #333333;
}

.vessel-form-footer {
  border-bottom: 1px solid #cccccc;
  margin: 10px 0px;
  color: #6c757d;
}

.last-edited-by-line {
  color: #6c757d;
}

.seafarer-table .table .td.vessel-company-type {
  background-color: #edf3f7;
}

.toolbar-wrap {
  justify-content: end;

  >* {
    margin-top: 5px;
  }
}

.document-continer {
  background-color: #000;
  color: #ffffff;
  padding: 20px;

  .download-btn {
    float: right;
  }

  .icon-container {
    float: right;

    .paris2-icon {
      margin-right: 30px;
    }
  }

  .paris2-icon {
    cursor: pointer;
  }

  .page-number-container {
    display: contents;
  }

  .header-container {
    display: flex;
    position: sticky;
    z-index: 99999;
  }

  .spinner-document {
    width: 100%;
    height: 200px;
    text-align: center;
    padding-top: 150px;

    .d-flex {
      justify-content: center;
    }
  }
}

.pdf-style {
  margin: 20px;

  canvas {
    margin-left: auto;
    margin-right: auto;
    max-width: 75% !important;
    margin-bottom: 20px;
  }

  img {
    max-width: 75%;
    align-self: center;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }

  .react-pdf__Page__textContent {
    height: 100% !important;
  }
}

.td {
  display: inline-block;
  word-break: break-all;
}

.no-border {
  border-top: 0px solid;
}

.no-records-summary {
  border-top: 1px solid #e6e6e6;
  margin: 10px;
}

.contact-log-contact-date {
  font-weight: 600;
}

.update-contact-log-required-text {
  border-top: 2px solid #1f4a70;
  color: #333333;
  font-size: 16px;
  font-weight: 500;
}

.crew-list-heading {
  color: #1f4a70;
  margin: 5px;
  font-size: 24px;
  font-weight: 400;
}

.vessel-name-crew-list-heading {
  font-size: 24px;
  font-weight: 400;
}

.vessel-name-crew-planning-heading {
  font-size: 20px;
  font-weight: normal;
}

.date-search-crew-list {
  margin: 5px;
  width: 50%;
}

.action-button-edit-color {
  color: #1f4a70;
}

.vesselDropdown {
  margin-left: 1%;

  .vessel_dropdown {
    margin-left: 1rem;

    &__vessel-header-dropdown-switch {
      .form-control {
        justify-content: space-between !important;
        display: flex;
        min-width: 12rem;
        align-items: center;
        background: #fff 0% 0% no-repeat padding-box;
        border: 1px solid #1f4a70;
        border-radius: 4px;
        opacity: 1;
        font: normal normal medium 14px/17px Inter, sans-serif;
        letter-spacing: 0px;
        color: #1f4a70;
        height: 38px !important;
      }

      #dropdown-item-button {
        justify-content: space-between !important;
        display: flex;
        min-width: 12rem;
        align-items: center;
        background: #fff 0% 0% no-repeat padding-box;
        border: 1px solid #1f4a70;
        border-radius: 4px;
        opacity: 1;
        font: normal normal medium 14px/17px Inter, sans-serif;
        letter-spacing: 0px;
        color: #1f4a70;
      }
    }

    &__dropdown-icon {
      width: 16px;
    }
  }
}

.form-group {

  &.custom-picker,
  &.vessel_dropdown {
    display: inline-block;
  }
}

.link-underline {
  text-decoration: underline !important;
  color: #1f4a70;
  cursor: pointer;
}

.form-heading {
  color: #333333;
  font-weight: bolder;
  font-size: 16px;
}

.update-wages-table {
  .table thead tr th {
    border-top: 2px solid #1f4a70;
    border-bottom: 2px solid #1f4a70;
  }

  .table>tbody>tr:nth-child(odd)>td {
    background-color: #ffffff;
    border-top: none;
  }

  .table>tbody>tr:nth-child(even)>td {
    background-color: #f8f9fa;
    border-top: none;
  }

  .table>tbody>tr:last-child>td {
    border-bottom: 2px solid #cccccc;
  }

  .table>tbody>tr>td:first-child,
  .table>thead>tr>th:first-child {
    border-right: 1px solid #efefef;
  }
}

.update-wages-table-input-field {
  max-width: 175px;
  text-align: right;
}

.update-wages-modal-body {
  max-height: 55vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.main-table-title-with-bordertop {
  color: #1f4a70;
  font-weight: bolder;
  font-size: 16px;
  padding: 12px;
  border-top: 4px solid #1f4a70;
  .btn-sm {
    float: right;
  }
}

.sub-table-title-with-bordertop {
  color: #1f4a70;
  font-weight: bolder;
  font-size: 14px;
  padding: 12px;
}

.font-red {
  color: #d41b56;
}

.font-yellow {
  color: #ffa221;
}

.font-green {
  color: #28a747;
}

.font-deep-blue {
  color: #1e4a70;
}

.font-size-20 {
  font-size: 20px;
}

.seafarer-report-heading {
  color: #1f4a70;
  padding: 10px;
}

.modeller-footer {
  position: fixed;
  height: clamp(58px, 10vh, 80px);
  bottom: 0;
  width: 100%;
  background-color: #f8f9fa;
  z-index: 10;
  margin: 0px;
  padding: 5px;
  font-weight: 600;
  border-top: 1px solid #cccccc;

  .th:first-child {
    padding-left: 1.5em;
  }
}

.modeller-footer-columns {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: end;
  text-align: end;
  overflow-x: hidden;
  min-width: 130;
  background-color: #f8f9fa;
}

.dummy-footer-padding-compensation {
  background-color: #f8f9fa;
  height: clamp(58px, 10vh, 80px);
  border-top: 1px solid #cccccc;
  width: 25px;
  position: absolute;
  right: 0px;
  bottom: 0px;
}

.footer-compensation {
  height: clamp(58px, 10vh, 65px);
}

.vessel-plan-page {
  .paris2-icon {
    cursor: pointer;
    float: right;

    svg {
      color: #1f4a70;
    }
  }
}

.sort-icon-compensation {
  padding-right: 35px !important;
}

.typeable-dropdown-icon-style {
  position: absolute;
  right: 2px;
  top: 5px;
}

.btn-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .travel-btn {
    margin-right: 1rem;
  }
  .tooltip {
    display: inline-block;
    position: relative;
  }
}

.unable-to-approve p {
  font-size: 20px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  float: left;

  span:first-child {
    display: flex;
  }

  .checkbox {
    height: 17px;
    width: 17px;
    margin-right: 0.25rem;
    accent-color: #1f4a70;
  }
  
}
.checkbox-secondary {
  width: 17px;
  height: 17px;
  accent-color: #1991B8;
  flex-shrink: 0;
}

.width-140 {
  width: 140px;
}

.error-msg {
  font-size: 12px;
  color: red;
}

.seafarer-list-btn-container {
  display: flex;
  align-items: end;
  justify-content: end;
  .travel-btn {
    margin-right: 1rem;
  }
}

.disabled-gray {
  background-color: #dedede !important;
  color: #666666 !important;
  border-color: #dedede !important;
}

.screen-overlay {
  position: fixed;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  cursor: not-allowed;
}

.footer-item-edited-by {
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
}

.supt-appraisal-score {
  &.exceed-expectation {
    color: #28a747;
  }

  &.meet-expectation {
    color: #ffa221;
  }

  &.below-expectation {
    color: #d41b56;
  }
}

.master-appraisal-table {
  .table thead tr th {
    color: #1f4a70;
    border-top: 2px solid #1f4a70;
    border-bottom: 2px solid #1f4a70;
    padding-left: 0;
  }

  .table>tbody>tr:nth-child(odd)>td {
    background-color: #ffffff;
    border-top: none;
  }

  .table>tbody>tr:nth-child(even)>td {
    background-color: #f8f9fa;
    border-top: none;
  }

  .table>tbody>tr:last-child>td {
    background-color: #ffffff;
    border-top: 2px solid #1f4a70;
    border-bottom: 2px solid #1f4a70;
  }

  .table>tbody>tr>td:first-child,
  .table>thead>tr>th:first-child {
    width: 40px;
  }
}

#add-training-tooltip {
  div.tooltip-inner {
    background-color: #333333;
    color: white !important;
  }
}

#appraisal-tooltip {
  div.tooltip-inner {
    background-color: #333333;
    color: #ffffff;
  }

  div.arrow::before {
    border-bottom-color: #333333;
    color: #ffffff;
  }

  opacity: 1;
}

.supt-appraisal-tooltip-text {
  div.tooltip-inner {
    white-space: pre;
    text-align: left;
  }
}

.signature {
  border-bottom: 1px solid #efefef;
  height: 75px;
  margin-bottom: 5px;
}

.master-appraisal-pdf-spacing {
  height: 50px;
}

.pdf-crew-list-heading {
  color: #1f4a70;
  margin: 5px;
  font-size: 14px;
}

.pdf-content-only {
  display: block;
  width: 270mm;
}

#pdf-content-part-1 {
  display: none;
}

#pdf-content-part-2 {
  display: none;
}

.pdf-content-navTitle {
  margin: auto;
  margin-left: 0px;
  font-weight: bold;
  color: #1f4a70;
  font-size: 16px;
}

.pdf-content-footer {
  color: #1f4a70;
  font: normal normal normal 14px Inter, sans-serif;
}

.pdf-table-font-size {
  font-size: 12px;
}

.border-bottom-table {
  tr:last-child {
    td {
      border-bottom: 1px solid #dee2e6;
    }
  }
  .btn-link {
    padding-left: 0;
  }
}

.master-appraisal-vessel-name {
  font-size: 14px;
  border: 0px;
}

.alert-success {
  padding-bottom: 0px;
}

.reports-export-to-excel-modal {
  .modal-body {
    .form-control {
      margin-bottom: 1rem;
    }

    .form-label {
      font-weight: 500;
    }

    textarea.form-control {
      height: 6rem;
    }
  }
}

.no-data-alert-icon {
  display: flex;
  justify-content: center;
  margin-top: 30px;

  p {
    margin-top: 5px;
  }
}

.no-data-alert-icon div:nth-child(1) {
  text-align: center;
}

.crew-planner-table {
  font-size: 14px;
  .button-link {
    font-size: 14px;
  }
  .td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .page-number-border {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 0px 8px;
  }

  .hr-1px {
    border: 1px solid #cccccc;
    margin-bottom: 0;
  }

  .page-num-active {
    text-decoration: underline;
  }

  .page-num-inactive {
    text-decoration: initial;
  }

  .page-num-disabled {
    color: #cccccc;
  }

  .page-num-enabled {
    color: #1f4a70;
  }

  .page-num {
    display: inline-block;
    cursor: pointer;
    margin: 8px;
    user-select: none;
  }

  .table {
    border-spacing: 0;
    width: 100%;
    padding-top: 20px;
    min-width: 0 !important;

    .th {
      background-color: #ffffff;
      margin: 0;
      padding: 0.75em 0.7em;
      border-bottom: 2px solid #1f4a70;
      font-weight: bold;
      user-select: none;
    }

    .th:first-child {
      padding-left: 1.5em;
    }

    .body .tr>.td {
      border-bottom: 1px solid #cccccc;
    }

    .body>.tr:hover>.td {
      background-color: #eff8fa;
    }

    .td {
      word-break: break-word;
      text-align: left;
      background-color: #ffffff;
      margin: 0;
      padding: 0.4rem 0.7rem;

      a {
        color: #1f4a70;
      }
    }

    .td:first-child {
      padding-left: 1.5em;
    }

    &.sticky {
      overflow-x: auto;

      .footer {
        width: fit-content;
        bottom: 0;
      }

      .header {
        top: 0;
      }

      .body {
        position: relative;
      }

      [data-sticky-last-left-td] {
        border-right: 1px solid #cccccc;
      }
    }

    &.status_history {
      padding-top: 0;

      .th:last-child {
        text-align: left;
      }

      [data-sticky-last-left-td] {
        border-right: none;
        text-align: left;
      }
    }

    &.contract-expiry-list-table {
      overflow-x: auto;

      #top-header-0 {
        .th {
          border-bottom: none;
          border-bottom: 2px solid #1f4a70;
        }

        .th:nth-last-of-type(3) {
          border-right: 2px solid #cccccc;
        }

        .th:last-child {
          border-right: none;
          text-align: left;
        }
      }

      #top-header-1 {
        .th:nth-last-of-type(4) {
          border-right: 2px solid #cccccc;
        }

        .th:nth-last-of-type(7) {
          border-right: 2px solid #cccccc;
        }

        .th:last-child {
          text-align: left;
        }
      }

      .td:nth-last-of-type(3) {
        border-right: 2px solid #cccccc;
      }

    }

    &.vessel-plan {
      .td:nth-child(2) {
        border-right: 1px solid #cccccc;
      }
    }

    .load-overlay {
      height: 100%;
      width: 100%;
      z-index: 999;
      background-color: rgba(0, 0, 0, 0.2);
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .min-width-auto {
    min-width: auto !important;
  }
}

.box {
  text-align: center;
  border-radius: 5px;
  width: fit-content;
  padding: 0 10px;
  text-wrap: nowrap;
}

.red-box {
  color: #964B49;
  background: #F5CED1 0% 0% no-repeat padding-box;
}

.yellow-box {
  color: #C99A07;
  background: #FFF9E8 0% 0% no-repeat padding-box;
}

.green-box {
  color: #57BB70;
  background: #E9F6EC 0% 0% no-repeat padding-box;
}

.crew-planner-tab {
  .nav {
    display: flex;
    justify-content: flex-start;
    /* Align items to the start (left) */
    width: 100%;
    /* Ensure the nav takes the full width */
  }

  .nav-item {
    flex: 0 0 auto;
    /* Ensure NavItem takes only as much space as its content */
    text-align: center;
    
  }
}

.crew-planner-rank {
  background: #fafafa;
  padding: 15px;
  margin-bottom: 10px;
}

.crew-planner-initial-page {
  height: calc(100vh - 210px);
  border: 2px solid #DEDEDE;
  border-radius: 6px;
  padding: 10px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6C757D;
  flex-direction: column;
  text-align: center;

  .paris2-icon {
    margin-bottom: 15px;
    color: #1f4a70
  }
}

.managed-vessels-heading {
  color: #1f4a70;
  margin: 5px;
  margin-left: 25px;
  font-size: 20px;
  font-weight: 600;
}

.managed-vessels-dropdown {
  margin-bottom: 0rem !important;

  &__vessel-header-dropdown-switch {
    display: flex;
    align-items: center;

    .form-control {
      flex-grow: 1;
      justify-content: space-between !important;
      display: flex;
      min-width: 12rem;
      align-items: center;
      background: #fff 0% 0% no-repeat padding-box;
      border-radius: 4px;
      opacity: 1;
      font: normal normal medium 14px/17px Inter, sans-serif;
      letter-spacing: 0px;
    }

    .form-control:disabled {
      background: #e9ecef 0% 0% no-repeat padding-box;
      opacity: 1;
    }
  }

  &__dropdown-icon {
    width: 16px;
  }

}

.managed-vessels-action-required {
  background: #FFF9E8 0% 0% no-repeat padding-box;
  border-radius: 3px;
  opacity: 1;
  font-size: 0.8rem;
  letter-spacing: 0px;
  color: #BF7F05;
  text-align: center;
  padding: 5px;
}


.managed-vessels-action-not-required {
  background: #EFEFEF 0% 0% no-repeat padding-box;
  border-radius: 3px;
  opacity: 1;
  font-size: 0.8rem;
  letter-spacing: 0px;
  color: #6C757D;
  text-align: center;
  padding: 5px;
}

.managed-vessels-table {
  padding-top: 0px !important;
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.crew-management-heading {
  color: #1f4a70;
  margin: 5px;
  font-size: 20px;
  display: flex;

  .header-col {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

.crew-management-switch-vessel {
  margin-right: 12px;
  margin-bottom: 0px !important;
}

.crew-management-header-section {
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  padding: 16px;
  text-align: left;
  font-size: 0.9rem;

  .header {
    font-weight: bold;
  }
}

.crew-management-tab {
  padding-left: 15px;
  padding-right: 15px;
  font-size: 0.9rem;
}

.crew-management-crew-details-section {
  border: 1px solid #ccc;
  border-radius: 4px;
}

.crew-management-crew-details-title {
  padding: 0.7rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.refresh-button {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.crew-management-crew-details-list {
  height: 75vh;
  font-size: 0.8rem !important;

  .col {
    width: auto;
  }

  .row {
    margin: 0px;
  }
}

.seafarer-card {
  flex-wrap: nowrap;
  margin: 10px;
  padding: 10px;
  border: 2px solid #DEE2E6;
  border-radius: 5px;
  border-left-width: 5px;
  border-left-style: solid;

  .alert {
    margin-bottom: 0px;
    padding: .25rem 0.75rem;
    width: 100%;
  }
}

.seafarer-card-status-green {
  border-left-color: #28A745;
}

.seafarer-card-status-orange {
  border-left-color: #FFC107;
}

.seafarer-card-status-red {
  border-left-color: #DC3545;
}

.seafarer-card-status-blue {
  border-left-color: #4f9bff;
}

.seafarer-card-content {
  flex-wrap: nowrap;
  margin: 5px;
  padding: 5px;
  width: 100%;

  .btn {
    text-align: left;
  }
}

.seafarer-card-text {
  font-size: 0.8rem !important;
}

.seafarer-card-content-main {
  width: auto;
  flex-grow: 1;
}

.card-header {
  &.text-primary {
    background: none;
  }
}

.crew-management-alert {
  padding: .75rem 1.25rem;
  margin: 10px;
}

.remark-cell {
  display: flex;
  flex-direction: row;
}

.remark-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 215px;
}

.remark-pencil-icon {
  cursor: pointer;
  display: none;
}

.remark-empty {
  transition: opacity 0.4s ease;
}

.remark-cell:hover {
  .remark-pencil-icon {
    display: inline-block;
    opacity: 1;
    transition: opacity 0.3s ease;
  }
  .remark-empty {
    display: none;
  }
}

.crew-management-crew-details-loader-wrapper {
  display: flex;
  justify-content: center;
  padding: 10px;
}

.travelling-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #E9F6EC;
  text-align: center;

  svg {
    transform: rotate(45deg);
  }
}

/* 
  Badge Status
*/
.status-badge {
  padding: .25rem 0.50rem;
  font-size: 12px;
  line-height: 1;
  font-weight: 500;
  border-radius: 3px;
  border: none;
  margin-right: 12px;

  &-warning {
    color: #C99A07;
    background: #FFF9E8 0 0 no-repeat padding-box;
  }

  &-success {
    color: #57BB70;
    background: #E9F6EC 0 0 no-repeat padding-box;
  }
    &-danger {
      color: #C82333;
      background: #FAF2F5 0 0 no-repeat padding-box;
    }
}

.status-indicator {
  display: flex;
  align-items: center;
}

.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 8px;
  margin-left: 20px;
  border-radius: 50%;

  &-green {
    background-color: #28A747;
  }

  &-gray {
    background-color: #6C757D;
  }

  &-red {
    background-color: red;
  }
}


.travelling-icon-orange {
  background-color: #FFF9E8;
}

.seafarer-card-pending-status {
  padding: 2px 5px;
  background-color: #FFF9E8;
  color: #ffa221;
  border-radius: 3px;
}

.seafarer-card-success-status {
  padding: 2px 5px;
  background-color: #E8F5EB;
  color: #218838;
  border-radius: 3px;
}

.seafarer-card-expired-status{
  padding: 2px 5px;
  background-color: #FAF2F5;
  color: #C82333;
  border-radius: 3px;
}

.seafarer-card-additional-crew {
  padding: 2px 5px;
  background-color: #E5F1FF;
  color: #2D8CFF;
  border-radius: 3px;
  margin-right: 5px;
}

.seafarer-card-button {
  padding: 0.3rem 0.3rem;
  font-size: 14px;
}

.training-req-modal-form-group {
  min-width: 49%;
}

.wages-input {
  text-align: right;
  &::-webkit-outer-spin-button, &::-webkit-inner-spin-button { 
    margin-left: 0.5em;
  } 
}
.managed-vessels-vessel-row {
  margin-left: 15px;
  margin-top: 10px;
  margin-bottom: 10px;
  margin-right: 15px;
   
  .form-group {
    margin: 0;
  }
}

.managed-vessels-search-icon {
  background-color: #fff;
  border-right: none;
  margin-right: -3px;
}

.managed-vessels-vessel-search {
  padding-right: 6px;
  padding-left: 6px;
}

.managed-vessels-table,
.managed-vessels-table-wrapper,
.managed-vessel-table-vessel-name {
  font-size: 14px;
}

.confirm-recommend-modal .modal-dialog {
  max-width: 80% !important;
}

.seafarer-profile-card-crew-recommendation {
  display: grid;
  justify-content: center;
  text-align: center;
}

.seafarer-profile-card-crew-recommendation div {
  display: flex;
  justify-content: center;
}

.crew-recommend-modal-seafarer-details-wrapper {
  border: #dadada 1px solid;
  margin: 10px 0px 10px 0px;
  border-radius: 5px;
  padding-top: 20px;
}

.seafarer-profile-card-crew-recommendation-wrapper {
  min-width: 20%;
  width: 25%;
  max-width: 250px;
  height: 150px;
  display: flex;
  place-items: center;
  justify-content: center;
  border-right: 1px solid #dadada;
}

.red-border {
  border: #d41b56 1px solid;
}

.planning-relieve-date-bg {
  width: fit-content;
  height: fit-content;
  border-radius: 3px;
  padding: 0px 3px 0px 3px;
}

.red-box-planning-relieve {
  color: #964B49;
  background: #F5CED1 0% 0% no-repeat padding-box;
}

.orange-box-planning-relieve {
  color: #ffa221;
  background: #FFF9E8 0% 0% no-repeat padding-box;
}

.green-box-planning-relieve {
  color: #57BB70;
  background: #E9F6EC 0% 0% no-repeat padding-box;
}

.blue-box-planning-relieve {
  color: #1F4A70;
  background: #d1ecf1 0% 0% no-repeat padding-box;
}

.planning-relieve-modal-headings {
  font-weight: 500;
}

.onboarding-with-joint-experience {
  color: #6C757D;
  background: #efefef 0% 0% no-repeat padding-box;
}

.alert-icon-crew-recommendation-modal {
  margin-top: 3px;
}

.group-button-selector-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: end;
  margin-top: 20px;
}

.confirm-bottom-bar {
  position: fixed;
  bottom: 0;
  height: 70px;
  width: 100vw;
  display: flex;
  justify-content: center;
  place-items: center;
  z-index: 999;
  background-color: #F8F9FA;
  border-top: 2px solid #dadada;
  margin-left: -15px;
}

.confirm-bottom-button {
  width: 300px;
  height: fit-content;
}

.confirm-bottom-plan-to-relieve {
  background-color: #1991B8;
}

// utils to center
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; 
}
.seafarer-recommend-modal-name-wrapper {
  text-decoration: underline;
  cursor: pointer;
}

.planner-kpi-container {
  margin: 10px -30px 20px;
  padding: 15px 30px;
    background-color: #F3F6F8;
}

.kpi-card {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 12px;
  border: 1px solid #DEE2E6;
  border-radius: 6px;
  background-color: #fff;
  &:last-child {
    margin-right: 0;
  }
  .summerized-number {
    padding: 10px 16px 10px 20px;
    border-right: 1px solid #DEE2E6;
    font-size: 24px;
    color: #1f4a70;
  }
  .kpi-title {
    padding-right: 20px;
    padding-left: 16px;
    white-space: nowrap;
  }
}

.empty-avatar {
  border: 1px dashed #B0B6BA;
  background-color: #3C9EF51A;
  color: #A7A7A7;
}

.kpi-card-hyphen {
  font-weight: bold;
  color: #1F4A70;
}

.vessel-budget-kpi-card {
  height: 80px;
  display: grid !important;
}

.vessel-budget-value {
  padding-right: 20px;
  padding-left: 16px;
  white-space: nowrap;
  color: #1F4A70;
  font-size: 24px;
  margin-top: -20px;
}

.vessel-budget-kpi-container {
  padding: 15px;
  background-color: #f8f9fa;
  margin-top: 15px;
}

.second-table-filters {
  display: flex;
  justify-content: space-between;
  place-items: end;
}

.average-score-wrapper {
  border-radius: 20px;
  padding: 1px 30px;
  width: fit-content;
  height: fit-content;
  color: white;
}

.average-score-green {
  background-color: #28a747;
}

.average-score-green-light {
  background-color: #7BD390;
  color: #333;
}

.average-score-yellow {
  background-color: #FFC106;
  color: #333;
}

.average-score-orange {
  background-color: #FF8A01;
}

.average-score-red {
  background-color: #D41B56;
}

.tooltip-scores-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.tooltip-score-title {
  margin-right: 30px;
  white-space: nowrap;
}

.score-tooltip > .tooltip-inner {
  max-width: 100% !important;
  padding: 20px 20px 10px 20px;
}

.seafarer-score-loader {
  color: #1f4a70 !important;
  height: 1.5rem;
  width: 1.5rem;
}

.font-size-14 {
  font-size: 14px;
}

.font-weight-bold {
  font-weight: bold;
}

.managed-vessels-vessel-row .custom-input{
  background-color: transparent;
  white-space: nowrap;
  border: 0px;
  box-shadow: none;
  cursor: inherit;
  outline: none;
  padding: 0px;
  width: 100%;
}
.underline-text {
  color: #1F4A70;
  text-decoration: underline;
  font-size: 14px;
  cursor: pointer;
}

.view-clubbing-history {
  color: #1F4A70;
  font-weight: 400;
  text-decoration: underline;
  cursor: pointer;
}

.position-middle {
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
}

.react-datepicker-popper {
  z-index: 100;
}

.crew-list-table-column-wrapper {
  display: flex;
  place-items: center;
  justify-content: space-between;
}

.seafarer-list-count-crew-list {
  margin-top: 10px;
  margin-left: 15px;
}

.ocimf-checkbox-wrapper {
  display: flex;
  margin-top: 18px;
  margin-right: 15px;
}

.ocimf-checkbox {
  margin-right: 10px;
  width: 20px;
}

.ocimf-administration {
  padding: 5px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  width: fit-content;
}

.ocimf-status-pills-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.ocimf-status-pill {
  border-radius: 50px;
  width: fit-content;
  padding: 2px 10px;
  margin-bottom: 5px;
  margin-right: 5px;
  font-size: 12px;
  word-break: normal;
  font-weight: 500;
}

[data-sticky-first-right-td] {
  border-left: 1px solid #cccccc;
}

.length-of-contract {
  padding: 2px 8px;
  border-radius: 50px;
  background: #EDF3F7;
  text-align: center;
  max-width: fit-content;
}

.ocimf-tooltip-header {
  font-weight: bold;
}

.ocimf-tooltip-single-content-wrapper {
  display: grid;
  margin-bottom: 10px;
}

.ocimf-tooltip-wrapper {
  display: grid;
  text-align: left;
}

.heading-wrapper-crew-list {
  display: flex;
}

.date-of-search-wrapper input {
  font-size: 16px;
}

.underline {
  text-decoration: underline;
}

.vessel_dropdown {
  width: 306px !important;
  margin-top: 2px;
}

.vessel_dropdown__vessel-header-dropdown-switch .form-control {
  height: 38px !important;
  border: 1px solid #ced4da !important;
}

#typeahead-wrapper {
  transform: translate3d(0px, 38.125px, 0px) !important;
}

.rbt-aux .btn-close {
  font-weight: 400;
}

.rbt-aux .btn-close span {
  color: #1F4A70;
}

input[type=checkbox] {
  accent-color: #0091b8 !important;
}

input[type="checkbox"]:checked::after {
  display: block;
  font-size: 10px;
  color: white;
  text-align: center;
  line-height: 20px;
  transform: scale(0.8);
  position: relative;
}

.seafarer-table div {
  font-size: 14px;
}

.ocimf-checkbox-wrapper label {
  margin-bottom: 0;
}

