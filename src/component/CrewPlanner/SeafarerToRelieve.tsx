/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { Container } from 'react-bootstrap';
import moment from 'moment-timezone';
import { flatten } from 'lodash';
import { capitalizeFirstLetter } from '@src/model/utils';
import {
  CREW_PLANNING_STATUS_MAPPING,
  SECOND_TABLE_FILTERS,
  STATUS,
} from '@src/constants/crewPlanner';
import { Icon } from '@src/styleGuide';
import { getAvailableSeafarersColumns, getSeafarerToRelieveColumns } from './Column';
import DynamicFilter, { FilterConfig } from './DynamicFilter';
import GroupButtonSelector from '../common/GroupButtonSelector';
import './style.scss';
import InfinteScrollTable from '../common/InfiniteScrollTable';
import { getIsClubbedOrUnclubbed, getRank } from './utils';
import { getVesselType } from '../../pages/CrewPlanner/utils';

export const getSeafarerToRelieveFilter = (selectedRanks = [], isClubbedOrUnclubbed = false) =>
  [
    {
      type: 'search',
      props: {
        placeholder: 'Search by Name or HKID',
      },
      name: 'search',
      queryKey: 'keyword',
      className: 'input-col first-col',
      inputType: 'string',
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Rank',
        isSearchable: true,
        multiple: true,
      },
      name: 'rank',
      dataKey: 'seafarer.ranks',
      queryKey: 'seafarer_person:seafarer_status_history:seafarer_rank.value',
      apiQueryValueSelector: (value, data) =>
        value?.map((v) => data.find((d) => d.id === v)?.value) ?? [],
      inputType: 'array_number',
      className: 'input-col',
      seperator: '|',
      defaultValueOnLoad: selectedRanks?.filter(Boolean)?.join(','),
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Vessel',
        isSearchable: true,
        multiple: true,
      },
      name: 'vessel',
      dataKey: 'vessels',
      queryKey: 'seafarer_person:seafarer_status_history.vessel_id',
      inputType: 'array_number',
      className: 'input-col',
      seperator: '|',
      hidden: isClubbedOrUnclubbed,
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Owner',
        isSearchable: true,
        multiple: true,
      },
      name: 'owner',
      dataKey: 'vessel.owners',
      queryKey: 'vessel_ownership_id',
      apiQueryValueSelector: (value, data) => {
        const resp = flatten(value?.map((v) => data.find((d) => d.id === v)?.ownership_ids));
        if (value?.length && !resp.length) {
          return ['no-data'];
        }
        return resp;
      },
      inputType: 'array_number',
      className: 'input-col',
      global: true,
      hidden: isClubbedOrUnclubbed,
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Vessel Category',
        isSearchable: true,
        // multiple: true,
      },
      name: 'vessel_category',
      dataKey: 'vessel.vesselTypes',
      queryKey: 'vessel_type',
      apiQueryValueSelector: (value, data) => data.find((d) => d.id === value)?.value,
      inputType: 'string',
      className: 'input-col',
      global: true,
      hidden: isClubbedOrUnclubbed,
      // seperator: '|',
    },
    {
      type: 'date_range',
      props: {
        placeholderText: 'Date Range',
        dateFormat: 'd MMM yyyy',
        isClearable: true,
      },
      name: 'due_off_date',
      dataKey: 'tech_group.tech_group',
      queryKey: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
      inputType: 'array_date',
      dateFormat: 'YYYY-MM-DD',
      apiQueryValueSelector: (value) =>
        value.map((v) => (v ? moment(v).format('YYYY-MM-DD') : null)),
      className: 'input-col',
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Tech Group',
        isSearchable: true,
        multiple: true,
      },
      name: 'tech_group',
      dataKey: 'tech_group.tech_group',
      queryKey: 'seafarer_person:seafarer_status_history.vessel_tech_group',
      inputType: 'array_string',
      className: 'input-col',
      global: true,
      seperator: '|',
      hidden: isClubbedOrUnclubbed,
    },
    {
      name: 'crew_planning_status',
      shouldNotRenderInFilters: true,
      queryKey: 'crew_planning_status',
      inputType: 'array_string',
      className: 'input-col',
      global: true,
    },
  ] as FilterConfig[];

const getName = (status) => {
  if ([STATUS.clubbed, STATUS.club_confirmed].includes(status)) {
    return 'Clubbed Seafarers due for Relieve';
  }
  if ([STATUS.unclubbed].includes(status)) {
    return 'Unclubbed Seafarers due for Relieve';
  }
  return '1. Select the Seafarer to Relieve';
};

const SeafarerToRelieve = React.memo(
  ({
    selectedSeafarer,
    relieverSectionData,
    fetchRelieverData,
    handlePaginationOfReliever,
    handleSelectSeafarerToRelieve,
    reliverSectionLoading,
    selectedSeafarerToBeReliever,
    setSelectedSeafarerToBeReliever,
    relieverSectionPagination,
    filterData,
    setApiQuery,
    selectedFilters,
    onFilterChange,
    onCrewPlanningFilterChange,
    selectedRanks,
    secondTableLoadType,
    handleSecondTableLoadType,
    seafarersTotalCount,
    tabName,
    suggestedSeafarersLoading,
    handleSearch,
    searchNotClicked,
    hasEditAccess,
    setIsAPIQueryInitialized,
    KPI,
    ...props
  }) => {
    const selectedVesselType = getVesselType(selectedSeafarer);
    const isSuggestedSeafarer = secondTableLoadType === Object.values(SECOND_TABLE_FILTERS)[0];
    const availableSeafarercolumns = getAvailableSeafarersColumns({
      eventTracker: props.eventTracker,
      handleAction: props.handleAction,
      handleRemark: props.handleRemark,
      handleSelectedSeafarer: setSelectedSeafarerToBeReliever,
      selectedSeafarer: selectedSeafarerToBeReliever,
      vesselType: selectedVesselType,
      activeKey: tabName,
      suggestedSeafarersLoading,
      isSuggestedSeafarer,
      hasEditAccess,
      currentRank: getRank(selectedSeafarer),
    });
    const seafarerTobeRelieveColumns = getSeafarerToRelieveColumns({
      eventTracker: props.eventTracker,
      handleRemark: props.handleRemark,
      handleSelectedSeafarer: handleSelectSeafarerToRelieve,
      selectedSeafarer,
      selectedFilters,
      hasEditAccess,
    });

    const { isClubbedOrUnclubbed, crewPlanningStatus } = getIsClubbedOrUnclubbed(selectedFilters);
    const filters = getSeafarerToRelieveFilter(selectedRanks, isClubbedOrUnclubbed);
    let hasMoreData;

    if (isSuggestedSeafarer) {
      hasMoreData = false;
    } else if (relieverSectionData.total) {
      hasMoreData = relieverSectionData.data.length < relieverSectionData.total;
    } else {
      hasMoreData = true;
    }
    return (
      <Container>
        <DynamicFilter
          filters={filters}
          data={filterData}
          setApiQuery={setApiQuery}
          selectedFilters={selectedFilters}
          onFilterChange={onFilterChange}
          selectedRanks={selectedRanks}
          eventTracker={props.eventTracker}
          handleSearch={handleSearch}
          setIsAPIQueryInitialized={setIsAPIQueryInitialized}
        />
        {KPI}
        {!selectedFilters?.rank?.length && (
          <div className="crew-planner-initial-page">
            <div>
              <Icon icon="crew-invert" size={100} />
            </div>
            <p>
              To plan Seafarers to Relief, start by
              <br /> selecting the Rank
            </p>
          </div>
        )}
        {selectedFilters?.rank?.length && (
          <div className="crew-planner-table">
            <div className="group-button-selector-wrapper mb-2">
              <p className="m-0">{getName(crewPlanningStatus)}</p>
              <GroupButtonSelector
                buttonLabels={Object.keys(CREW_PLANNING_STATUS_MAPPING)}
                onSelect={onCrewPlanningFilterChange}
                selected={capitalizeFirstLetter(
                  selectedFilters?.crew_planning_status?.[0]?.split(',')?.[0],
                )}
                count={seafarersTotalCount}
              />
            </div>
            <InfinteScrollTable
              noDataText={
                <>
                  Please click Search with the selected filters.
                  <br />
                  If there are no result, please update the filters and click Search again.
                </>
              }
              {...props}
              columns={seafarerTobeRelieveColumns}
              height={300}
            />
            {![STATUS.club_confirmed, STATUS.clubbed, STATUS.unclubbed].includes(
              crewPlanningStatus,
            ) ? (
              <>
                <div className="second-table-filters mt-4">
                  <p className="mb-0 mt-4">2. Select Seafarer to be the Reliever</p>
                  <GroupButtonSelector
                    buttonLabels={Object.values(SECOND_TABLE_FILTERS)}
                    onSelect={handleSecondTableLoadType}
                    selected={secondTableLoadType}
                    count={relieverSectionData.total ?? 0}
                  />
                </div>
                {selectedSeafarer ? (
                  <InfinteScrollTable
                    hasMoreData={hasMoreData}
                    init_sort={relieverSectionPagination.sortBy}
                    loading={reliverSectionLoading}
                    fetchData={handlePaginationOfReliever}
                    data={relieverSectionData.data ?? []}
                    columns={availableSeafarercolumns}
                    height={300}
                  />
                ) : (
                  <div
                    className="table sticky-table table-responsive crew-planner-table table- crew-planner-initial-page"
                    style={{ height: 300 }}
                  >
                    <div>
                      <Icon icon="crew-invert" size={100} />
                    </div>
                    <p>
                      Select an Onboard Seafarer to view the recommendation for Reliever Seafarer
                    </p>
                  </div>
                )}
              </>
            ) : null}
          </div>
        )}
      </Container>
    );
  },
);

export default SeafarerToRelieve;
